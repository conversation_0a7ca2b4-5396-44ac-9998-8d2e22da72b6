-- Remove description column from tenant_i18n_content table
-- This column is no longer needed as we only store customized keys

ALTER TABLE tenant_i18n_content DROP COLUMN IF EXISTS description;

-- Add comment to clarify the new purpose
COMMENT ON TABLE tenant_i18n_content IS 'Stores only customized i18n translations that differ from template files for each tenant';
COMMENT ON COLUMN tenant_i18n_content.translation_key IS 'Translation key in dot notation (e.g., nav.services) - only keys that differ from template';
COMMENT ON COLUMN tenant_i18n_content.translation_value IS 'Customized translated text value that overrides template value';
