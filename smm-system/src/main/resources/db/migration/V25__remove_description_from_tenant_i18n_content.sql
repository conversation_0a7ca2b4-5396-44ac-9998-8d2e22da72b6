-- Remove unnecessary columns from tenant_i18n_content table
-- This table now only stores customized keys that differ from template files
-- Timestamp tracking will be handled via Redis

ALTER TABLE tenant_i18n_content DROP COLUMN IF EXISTS description;
ALTER TABLE tenant_i18n_content DROP COLUMN IF EXISTS created_at;
ALTER TABLE tenant_i18n_content DROP COLUMN IF EXISTS updated_at;

-- Add comment to clarify the new purpose
COMMENT ON TABLE tenant_i18n_content IS 'Stores only customized i18n translations that differ from template files for each tenant. Timestamps handled via Redis.';
COMMENT ON COLUMN tenant_i18n_content.translation_key IS 'Translation key in dot notation (e.g., nav.services) - only keys that differ from template';
COMMENT ON COLUMN tenant_i18n_content.translation_value IS 'Customized translated text value that overrides template value';
