package tndung.vnfb.smm.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.dto.ApiResponseEntity;
import tndung.vnfb.smm.dto.request.AdminPanelUserReq;
import tndung.vnfb.smm.dto.request.AdminOrderSearchReq;
import tndung.vnfb.smm.dto.request.ExtendTenantSubscriptionReq;
import tndung.vnfb.smm.dto.request.AdminBalanceOperationReq;
import tndung.vnfb.smm.dto.response.AdminPanelTenantRes;
import tndung.vnfb.smm.dto.response.AdminPanelUserRes;
import tndung.vnfb.smm.dto.response.AdminOrderRes;
import tndung.vnfb.smm.dto.response.DomainInfoRes;
import tndung.vnfb.smm.dto.response.TransactionRes;
import tndung.vnfb.smm.service.AdminPanelService;

import javax.validation.Valid;

@RestController
@RequestMapping("/v1/admin-panels")
@RequiredArgsConstructor
@Slf4j
public class AdminPanelController {

    private final AdminPanelService adminPanelService;

    /**
     * Get all panels (tenants with main = false)
     */
    @GetMapping("/panels")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<AdminPanelTenantRes>> getAllPanels(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        log.info("Getting all panels - page: {}, size: {}, search: {}", page, size, search);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        Page<AdminPanelTenantRes> panels = adminPanelService.getAllPanels(page, size, search);
        return ApiResponseEntity.success(panels);
    }

    /**
     * Get all users from main tenant
     */
    @GetMapping("/main-tenant-users")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<AdminPanelUserRes>> getMainTenantUsers(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        log.info("Getting main tenant users - page: {}, size: {}, search: {}", page, size, search);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        Page<AdminPanelUserRes> users = adminPanelService.getMainTenantUsers(page, size, search);
        return ApiResponseEntity.success(users);
    }

    /**
     * Add money to a main tenant user
     */
    @PostMapping("/add-money")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<TransactionRes> addMoneyToUser(@RequestBody @Valid AdminPanelUserReq request) {
        log.info("Adding money to user: {}, amount: {}", request.getUserId(), request.getAmount());
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        TransactionRes transaction = adminPanelService.addMoneyToUser(request);
        return ApiResponseEntity.success(transaction);
    }

    /**
     * Get tenant details by ID
     */
    @GetMapping("/panels/{tenantId}")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelTenantRes> getTenantById(@PathVariable String tenantId) {
        log.info("Getting tenant by ID: {}", tenantId);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        AdminPanelTenantRes tenant = adminPanelService.getTenantById(tenantId);
        return ApiResponseEntity.success(tenant);
    }

    /**
     * Get user details by ID
     */
    @GetMapping("/users/{userId}")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelUserRes> getUserById(@PathVariable Long userId) {
        log.info("Getting user by ID: {}", userId);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        AdminPanelUserRes user = adminPanelService.getUserById(userId);
        return ApiResponseEntity.success(user);
    }

    /**
     * Get domain information by tenant ID
     */
    @GetMapping("/panels/{tenantId}/info")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<DomainInfoRes> getDomainInfo(@PathVariable String tenantId) {
        log.info("Getting domain info for tenant ID: {}", tenantId);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        DomainInfoRes domainInfo = adminPanelService.getDomainInfo(tenantId);
        return ApiResponseEntity.success(domainInfo);
    }

    /**
     * Get all orders from all tenants
     */
    @GetMapping("/orders")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<AdminOrderRes>> getAllOrders(
            AdminOrderSearchReq searchReq,
            Pageable pageable) {
        log.info("Getting all orders with search criteria: {}", searchReq);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        Page<AdminOrderRes> orders = adminPanelService.getAllOrders(searchReq, pageable);
        return ApiResponseEntity.success(orders);
    }

    /**
     * Extend tenant subscription
     */
    @PutMapping("/panels/{tenantId}/extend-subscription")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelTenantRes> extendTenantSubscription(
            @PathVariable String tenantId,
            @RequestBody @Valid ExtendTenantSubscriptionReq request) {
        log.info("Extending subscription for tenant: {} by {} days", tenantId, request.getExtensionDays());
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        AdminPanelTenantRes tenant = adminPanelService.extendTenantSubscription(tenantId, request);
        return ApiResponseEntity.success(tenant);
    }

    /**
     * Disable tenant
     */
    @PutMapping("/panels/{tenantId}/disable")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelTenantRes> disableTenant(@PathVariable String tenantId) {
        log.info("Disabling tenant: {}", tenantId);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        AdminPanelTenantRes tenant = adminPanelService.disableTenant(tenantId);
        return ApiResponseEntity.success(tenant);
    }

    /**
     * Enable tenant
     */
    @PutMapping("/panels/{tenantId}/enable")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<AdminPanelTenantRes> enableTenant(@PathVariable String tenantId) {
        log.info("Enabling tenant: {}", tenantId);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        AdminPanelTenantRes tenant = adminPanelService.enableTenant(tenantId);
        return ApiResponseEntity.success(tenant);
    }

    /**
     * Delete tenant
     */
    @DeleteMapping("/panels/{tenantId}")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<String> deleteTenant(@PathVariable String tenantId) {
        log.info("Deleting tenant: {}", tenantId);
        adminPanelService.deleteTenant(tenantId);
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        return ApiResponseEntity.success("Tenant deleted successfully");
    }

    /**
     * Add balance to user
     */
    @PostMapping("/users/{userId}/add-balance")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<TransactionRes> addBalanceToUser(
            @PathVariable Long userId,
            @RequestBody @Valid AdminBalanceOperationReq request) {
        log.info("Adding balance to user: {} amount: {}", userId, request.getAmount());
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        TransactionRes transaction = adminPanelService.addBalanceToUser(userId, request);
        return ApiResponseEntity.success(transaction);
    }

    /**
     * Deduct balance from user
     */
    @PostMapping("/users/{userId}/deduct-balance")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<TransactionRes> deductBalanceFromUser(
            @PathVariable Long userId,
            @RequestBody @Valid AdminBalanceOperationReq request) {
        log.info("Deducting balance from user: {} amount: {}", userId, request.getAmount());
        TenantContext.setCurrentTenant(Common.MAIN_TENANT);
        TransactionRes transaction = adminPanelService.deductBalanceFromUser(userId, request);
        return ApiResponseEntity.success(transaction);
    }

    /**
     * Get user transaction history
     */
    @GetMapping("/users/{userId}/transactions")
    @PreAuthorize("hasRole('ROLE_ADMIN_PANEL')")
    public ApiResponseEntity<Page<TransactionRes>> getUserTransactions(
            @PathVariable Long userId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        log.info("Getting transaction history for user: {} page: {} size: {}", userId, page, size);   TenantContext.setCurrentTenant(Common.MAIN_TENANT);

        Page<TransactionRes> transactions = adminPanelService.getUserTransactions(userId, page, size);
        return ApiResponseEntity.success(transactions);
    }
}
