package tndung.vnfb.smm.entity.audit.listener;

import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.entity.audit.*;

import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.time.OffsetDateTime;

public class TenantAuditListener {

    @PrePersist
    public void prePersist(Object entity) {
        // Gán tenant_id nếu entity là AbstractTenantEntity
        if (entity instanceof AbstractTenantEntity tenantEntity) {
            String tenantId = TenantContext.getWildcardTenant();
            if (tenantId != null) {
                tenantEntity.setTenantId(tenantId);
            } else {
                throw new IllegalStateException("Tenant ID must be set in TenantContext before persisting entity");
            }
            tenantEntity.setCreatedAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));
            tenantEntity.setUpdatedAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));
        }

        else if (entity instanceof AbstractTenantCreatedEntity auditEntity) {
            String tenantId = TenantContext.getWildcardTenant();
            if (tenantId != null) {
                auditEntity.setTenantId(tenantId);
            } else {
                throw new IllegalStateException("Tenant ID must be set in TenantContext before persisting entity");
            }
            auditEntity.setCreatedAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));

        }

        // Gán createdAt nếu entity là AbstractCreatedAuditEntity
        else if (entity instanceof AbstractCreatedAuditEntity auditEntity) {
            auditEntity.setCreatedAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));
        }

        else if (entity instanceof AbstractOnlyTenantEntity auditEntity) {
            String tenantId = TenantContext.getWildcardTenant();
            if (tenantId != null) {
                auditEntity.setTenantId(tenantId);
            } else {
                throw new IllegalStateException("Tenant ID must be set in TenantContext before persisting entity");
            }
        }

        // Gán updatedAt nếu entity là AbstractAuditEntity

    }

    @PreUpdate
    public void preUpdate(Object entity) {
        // Gán tenant_id nếu entity là AbstractTenantEntity
        if (entity instanceof AbstractTenantEntity tenantEntity) {
//            String tenantId = TenantContext.getWildcardTenant();
//            if (tenantId != null) {
//                tenantEntity.setTenantId(tenantId);
//            } else {
//                throw new IllegalStateException("Tenant ID must be set in TenantContext before updating entity");
//            }
            tenantEntity.setUpdatedAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));
        }

        // Gán updatedAt nếu entity là AbstractAuditEntity
        if (entity instanceof AbstractAuditEntity auditEntity) {
            auditEntity.setUpdatedAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));
        }
    }
}