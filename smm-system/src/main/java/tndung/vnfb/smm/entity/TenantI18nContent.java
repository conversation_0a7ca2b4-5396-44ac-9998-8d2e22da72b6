package tndung.vnfb.smm.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import tndung.vnfb.smm.entity.audit.AbstractTenantEntity;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * Entity representing custom i18n content for each tenant
 */
@Entity
@Table(name = "tenant_i18n_content",
       uniqueConstraints = @UniqueConstraint(columnNames = {"tenant_id", "language_code", "translation_key"}))
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantI18nContent extends AbstractTenantEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "language_code", nullable = false, length = 10)
    private String languageCode;

    @Column(name = "translation_key", nullable = false, length = 500)
    private String translationKey;

    @Column(name = "translation_value", columnDefinition = "TEXT")
    private String translationValue;

    @Column(name = "description", length = 1000)
    private String description;

    public TenantI18nContent(String languageCode, String translationKey, String translationValue) {
        this.languageCode = languageCode;
        this.translationKey = translationKey;
        this.translationValue = translationValue;
    }
}
