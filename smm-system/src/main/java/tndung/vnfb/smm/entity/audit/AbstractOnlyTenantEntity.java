package tndung.vnfb.smm.entity.audit;

import lombok.Getter;
import lombok.Setter;
import tndung.vnfb.smm.entity.audit.listener.TenantAuditListener;

import javax.persistence.Column;
import javax.persistence.EntityListeners;
import javax.persistence.MappedSuperclass;

@MappedSuperclass
@Getter
@Setter
@EntityListeners(TenantAuditListener.class)
public class AbstractOnlyTenantEntity {

    @Column(name = "tenant_id", nullable = false, updatable = false)
    private String tenantId;
}
