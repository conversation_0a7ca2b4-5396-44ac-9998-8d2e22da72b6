package tndung.vnfb.smm.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import tndung.vnfb.smm.entity.audit.AbstractTenantCreatedEntity;

import javax.persistence.*;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZonedDateTime;

@Entity
@Table(name = "password_reset_tokens")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FilterDef(name = "tenantFilter", parameters = { @ParamDef(name = "tenantId", type = "string") ,  @ParamDef(name = "wildcardTenant", type = "string") })
@Filter(name = "tenantFilter", condition = "(tenant_id = :tenantId  OR tenant_id = :wildcardTenant)")
public class PasswordResetToken extends AbstractTenantCreatedEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    

    
    @Column(name = "token", nullable = false, unique = true)
    private String token;
    
    @Column(name = "email", nullable = false)
    private String email;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private GUser user;
    
    @Column(name = "expires_at", nullable = false)
    private OffsetDateTime expiresAt;

    
    @Column(name = "used", nullable = false)
    @Builder.Default
    private Boolean used = false;
    
    @Column(name = "used_at")
    private OffsetDateTime usedAt;
    
    public boolean isExpired() {
        return OffsetDateTime.now().isAfter(expiresAt);
    }
    
    public boolean isValid() {
        return !used && !isExpired();
    }
}
