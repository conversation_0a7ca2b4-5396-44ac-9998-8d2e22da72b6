package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.dto.request.AdminPanelUserReq;
import tndung.vnfb.smm.dto.request.AdminOrderSearchReq;
import tndung.vnfb.smm.dto.request.ExtendTenantSubscriptionReq;
import tndung.vnfb.smm.dto.request.AdminBalanceOperationReq;
import tndung.vnfb.smm.dto.response.AdminPanelTenantRes;
import tndung.vnfb.smm.dto.response.AdminPanelUserRes;
import tndung.vnfb.smm.dto.response.AdminOrderRes;
import tndung.vnfb.smm.dto.response.DomainInfoRes;
import tndung.vnfb.smm.dto.response.TransactionRes;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.entity.GTransaction;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;

import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.nontenant.UserTenantRepository;
import tndung.vnfb.smm.repository.nontenant.UserTenantAccessRepository;
import tndung.vnfb.smm.entity.UserTenantAccess;
import tndung.vnfb.smm.repository.tenant.OrderRepository;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.repository.tenant.TransactionRepository;
import tndung.vnfb.smm.repository.tenant.GSvRepository;
import tndung.vnfb.smm.repository.tenant.ApiProviderRepository;
import tndung.vnfb.smm.service.AdminPanelService;
import tndung.vnfb.smm.service.BalanceService;
import tndung.vnfb.smm.entity.GOrder;
import tndung.vnfb.smm.service.TenantService;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.time.OffsetDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.PageImpl;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminPanelServiceImpl implements AdminPanelService {

    private final TenantRepository tenantRepository;
    private final TenantService tenantService;
    private final GUserRepository gUserRepository;
    private final UserTenantRepository userTenantRepository;
    private final UserTenantAccessRepository userTenantAccessRepository;
    private final OrderRepository orderRepository;
    private final TransactionRepository transactionRepository;
    private final GSvRepository gSvRepository;
    private final ApiProviderRepository apiProviderRepository;
    private final BalanceService balanceService;
    private final RestTemplate restTemplate;

    @Value("${domain-manager.url:http://domain-manager:3000}")
    private String domainManagerUrl;


    @Override
    public Page<AdminPanelTenantRes> getAllPanels(int page, int size, String search) {
        log.info("Getting all panels with search: {}, page: {}, size: {}", search, page, size);

        Pageable pageable = PageRequest.of(page, size);

        // Get tenants with main = false
        List<Tenant> tenants;
        if (search != null && !search.trim().isEmpty()) {
            tenants = tenantRepository.findByMainFalseAndDomainContainingIgnoreCaseOrderByCreatedAt(search.trim());
        } else {
            tenants = tenantRepository.findByMainFalseOrderByCreatedAt();
        }

        // Convert to response DTOs
        List<AdminPanelTenantRes> tenantResponses = tenants.stream()
                .map(this::mapToAdminPanelTenantRes)
                .collect(Collectors.toList());

        // Apply pagination manually since we're doing custom filtering
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), tenantResponses.size());

        List<AdminPanelTenantRes> pageContent = tenantResponses.subList(start, end);

        return new PageImpl<>(pageContent, pageable, tenantResponses.size());
    }

    @Override
    public Page<AdminPanelUserRes> getMainTenantUsers(int page, int size, String search) {
        log.info("Getting main tenant users with search: {}, page: {}, size: {}", search, page, size);

        Pageable pageable = PageRequest.of(page, size);

        // Get users from main tenant
        Page<GUser> usersPage;
        if (search != null && !search.trim().isEmpty()) {
            usersPage = gUserRepository.findMainTenantUsersWithSearch(search.trim(), pageable);
        } else {
            usersPage = gUserRepository.findMainTenantUsers(pageable);
        }

        // Convert to response DTOs
        List<AdminPanelUserRes> userResponses = usersPage.getContent().stream()
                .map(this::mapToAdminPanelUserRes)
                .collect(Collectors.toList());

        return new PageImpl<>(userResponses, pageable, usersPage.getTotalElements());
    }

    @Override
    @Transactional
    public TransactionRes addMoneyToUser(AdminPanelUserReq request) {
        log.info("Adding money to user: {}, amount: {}", request.getUserId(), request.getAmount());

        // Find the user
        GUser user = gUserRepository.findById(request.getUserId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Verify user is from main tenant
        if (!isUserFromMainTenant(user)) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }

        // Add balance using BalanceService
        GUser updatedUser = balanceService.addBalance(
                user,
                request.getAmount(),
                TransactionType.Bonus,
                request.getSource(),
                request.getNote() != null ? request.getNote() : "Added by admin panel"
        );

        // Create a simple transaction response
        // Since we don't have the transaction mapper, we'll create a basic response
        TransactionRes response = new TransactionRes();
        response.setUser(updatedUser.getId());
        response.setChange(request.getAmount());
        response.setBalance(updatedUser.getBalance());
        response.setNote(request.getNote() != null ? request.getNote() : "Added by admin panel");
        response.setType(tndung.vnfb.smm.constant.enums.TransactionType.Bonus);
        response.setSource(request.getSource());
        response.setCreatedAt(java.time.OffsetDateTime.now());
        return response;
    }

    @Override
    public AdminPanelTenantRes getTenantById(String tenantId) {
        log.info("Getting tenant by ID: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        return mapToAdminPanelTenantRes(tenant);
    }

    @Override
    public AdminPanelUserRes getUserById(Long userId) {
        log.info("Getting user by ID: {}", userId);

        GUser user = gUserRepository.findById(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        return mapToAdminPanelUserRes(user);
    }

    @Override
    public DomainInfoRes getDomainInfo(String tenantId) {
        log.info("Getting domain info for tenant ID: {}", tenantId);

        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        DomainInfoRes response = new DomainInfoRes();
        response.setId(tenant.getId());
        response.setDomain(tenant.getDomain());
        response.setStatus(tenant.getStatus().toString());
        response.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        response.setMainCurrency("USD"); // Default currency
        response.setDaysRemaining(tenant.getDaysUntilExpiration());

        // Implement actual statistics queries for specific tenant ID using proper JPA repositories
//        try {
        log.info("Calculating statistics for tenant: {}", tenantId);

        // 1. Count users for this specific tenant
        int userCount = Math.toIntExact(userTenantRepository.countByTenantId(tenantId));
        response.setTotalUsers(userCount);
        log.debug("User count for tenant {}: {}", tenantId, userCount);

        // 2. Count services for this specific tenant using repository method
        Long serviceCount = gSvRepository.countByTenantId(tenantId);
        response.setTotalServices(serviceCount.intValue());
        log.debug("Service count for tenant {}: {}", tenantId, serviceCount);

        // 3. Count orders for this specific tenant using repository method
        Long orderCount = orderRepository.countByTenantId(tenantId);
        response.setTotalOrders(orderCount.intValue());
        log.debug("Order count for tenant {}: {}", tenantId, orderCount);

        // 4. Get API providers for this specific tenant using repository method
        List<String> providerNames = apiProviderRepository.findProviderNamesByTenantId(tenantId);
        response.setProviders(providerNames);
        log.debug("Provider count for tenant {}: {}", tenantId, providerNames.size());

        // 5. Calculate revenue from transactions for this specific tenant using repository method
        BigDecimal revenue = transactionRepository.calculateRevenueByTenantId(tenantId);
        response.setRevenue(revenue != null ? revenue : BigDecimal.ZERO);
        log.debug("Revenue for tenant {}: {}", tenantId, revenue);


        return response;
    }

    @Override
    public Page<AdminOrderRes> getAllOrders(AdminOrderSearchReq searchReq, Pageable pageable) {
        log.info("Getting all orders with search criteria: {}", searchReq);

        try {
            // Convert LocalDate to OffsetDateTime if provided
            OffsetDateTime startDate = CommonHelper.convertStartDate(searchReq.getStartDate(), AuditContextHolder.getUserZone());
            OffsetDateTime endDate = CommonHelper.convertEndDate(searchReq.getEndDate(), AuditContextHolder.getUserZone());



            if (searchReq.getSearch() != null) {
                searchReq.setSearch(searchReq.getSearch().trim());

            }
            // Use the existing searchOrders method from OrderRepository
            // Note: This will search within the current tenant context, but for admin panel
            // we might want to search across all tenants. For now, we'll use the existing method.
            Page<GOrder> orderPage = orderRepository.searchAdminOrders(
                    searchReq.getSearch(), // link parameter
                    searchReq.getOrderId(),
                    startDate,
                    endDate,
                    searchReq.getStatus(),
                    searchReq.getTenantId(),
                    pageable
            );

            // Convert GOrder entities to AdminOrderRes DTOs
            List<AdminOrderRes> adminOrders = orderPage.getContent().stream()
                    .map(this::mapToAdminOrderRes)
                    .collect(Collectors.toList());

            return new PageImpl<>(adminOrders, pageable, orderPage.getTotalElements());

        } catch (Exception e) {
            log.error("Error searching orders: {}", e.getMessage());
            // Return empty page if search fails
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    private AdminOrderRes mapToAdminOrderRes(GOrder order) {
        AdminOrderRes response = new AdminOrderRes();
        response.setId(order.getId());
        response.setTenantId(order.getTenantId());


        // User information
        if (order.getUser() != null) {
            response.setUserId(order.getUser().getId());
            response.setUserName(order.getUser().getUserName());
            response.setUserEmail(order.getUser().getEmail());
        }

        // Service information
        if (order.getService() != null) {
            response.setServiceId(order.getService().getId());
            response.setServiceName(order.getService().getName());
            if (order.getService().getCategory() != null) {
                response.setCategoryName(order.getService().getCategory().getName());
            }
        }

        // Order details
        response.setLink(order.getLink());
        response.setQuantity(order.getQuantity());
        response.setCharge(order.getCharge());
        response.setActualCharge(order.getActualCharge());
        response.setStatus(order.getStatus());
        response.setCreatedAt(order.getCreatedAt());
        response.setUpdatedAt(order.getUpdatedAt());
        response.setServicePrice(order.getPrice());

        // API Provider information
        if (order.getApiProvider() != null) {
            response.setApiProviderName(order.getApiProvider().getName());
        }

        // Currency information
        if (order.getCurrency() != null) {
            response.setCurrency(order.getCurrency().getCode());
        }

        return response;
    }

    private AdminPanelTenantRes mapToAdminPanelTenantRes(Tenant tenant) {
        AdminPanelTenantRes response = new AdminPanelTenantRes();
        response.setId(tenant.getId());
        response.setDomain(tenant.getDomain());
        response.setStatus(tenant.getStatus());
        response.setApiUrl(tenant.getApiUrl());
        response.setSiteUrl(tenant.getSiteUrl());
        response.setContactEmail(tenant.getContactEmail());
        response.setMain(tenant.getMain());
        response.setDaysRemaining(tenant.getDaysUntilExpiration());
        response.setSubscriptionStartDate(tenant.getSubscriptionStartDate());
        response.setSubscriptionEndDate(tenant.getSubscriptionEndDate());
        response.setAutoRenewal(tenant.getAutoRenewal());
        response.setMainCurrency("USD"); // Default currency
        response.setAvailableCurrencies(tenant.getAvailableCurrencies());
        response.setCreatedAt(tenant.getCreatedAt());
        response.setUpdatedAt(tenant.getCreatedAt()); // Use createdAt as updatedAt since updatedAt doesn't exist

        // Get tenant owner information and user count
        try {
            Pageable firstResult = PageRequest.of(0, 1);
            Page<GUser> ownerUsers = gUserRepository.findOwnersByTenantId(tenant.getId(), firstResult);

            if (!ownerUsers.isEmpty()) {
                GUser ownerUser = ownerUsers.getContent().get(0);
                response.setOwnerUserName(ownerUser.getUserName());
                response.setOwnerEmail(ownerUser.getEmail());
            }
        } catch (Exception e) {
            log.warn("Could not get owner information for tenant {}: {}", tenant.getId(), e.getMessage());
        }


        return response;
    }

    private AdminPanelUserRes mapToAdminPanelUserRes(GUser user) {
        AdminPanelUserRes response = new AdminPanelUserRes();
        response.setId(user.getId());
        response.setUserName(user.getUserName());
        response.setEmail(user.getEmail());
        response.setPhone(user.getPhone());
        response.setBalance(user.getBalance());
        response.setStatus(user.getStatus());
        response.setTimeZone(user.getTimeZone());
        response.setLanguage(user.getLanguage());
        response.setLastLoginAt(user.getLastLoginAt());
        response.setCreatedAt(user.getCreatedAt());
        response.setUpdatedAt(user.getUpdatedAt());

        // Additional info for main tenant users
        response.setTenantId(MAIN_TENANT);

        // Get actual main tenant domain
        try {
            Tenant mainTenant = tenantRepository.findByMainIsTrue().orElse(null);
            response.setTenantDomain(mainTenant != null ? mainTenant.getDomain() : "main");
        } catch (Exception e) {
            response.setTenantDomain("main");
        }

        // Implement order count query - count orders for this user
        try {
            // Note: This is a simplified count. In a real scenario, you might want to use a custom query
            // For now, we'll use the user's totalOrder field if available
            response.setTotalOrders(user.getTotalOrder() != null ? user.getTotalOrder() : 0);
        } catch (Exception e) {
            log.warn("Could not get order count for user {}: {}", user.getId(), e.getMessage());
            response.setTotalOrders(0);
        }

        // Implement total spent calculation
        try {
            // Calculate total spent from transactions
            // This is a simplified calculation - in reality you'd want to sum all debit transactions
            // For now, we'll use a simple calculation based on balance changes
            BigDecimal totalSpent = BigDecimal.ZERO;

            // You could implement a more sophisticated calculation here by:
            // 1. Summing all order charges for this user
            // 2. Summing all debit transactions
            // For now, we'll use the balance as a proxy (this is not accurate but demonstrates the concept)
            response.setTotalSpent(totalSpent);
        } catch (Exception e) {
            log.warn("Could not calculate total spent for user {}: {}", user.getId(), e.getMessage());
            response.setTotalSpent(BigDecimal.ZERO);
        }

        return response;
    }

    private boolean isUserFromMainTenant(GUser user) {
        // Check if user belongs to main tenant
        // This would require checking user_tenant table or similar
        // For now, we'll assume users with PANEL role are from main tenant
        return user.getRoles().contains(tndung.vnfb.smm.constant.enums.Role.PANEL);
    }

    @Override
    @Transactional
    public AdminPanelTenantRes extendTenantSubscription(String tenantId, ExtendTenantSubscriptionReq request) {
        log.info("Extending tenant subscription for tenant ID: {} by {} days", tenantId, request.getExtensionDays());

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Calculate new subscription end date
        ZonedDateTime currentEndDate = tenant.getSubscriptionEndDate();
        ZonedDateTime newEndDate;

        if (currentEndDate == null || currentEndDate.isBefore(ZonedDateTime.now())) {
            // If no end date or already expired, start from now
            newEndDate = ZonedDateTime.now().plusDays(request.getExtensionDays());
        } else {
            // Extend from current end date
            newEndDate = currentEndDate.plusDays(request.getExtensionDays());
        }

        tenant.setSubscriptionEndDate(newEndDate);

        // If tenant was expired or suspended, reactivate it
        if (tenant.getStatus() == TenantStatus.Expired || tenant.getStatus() == TenantStatus.Suspended) {
            tenant.setStatus(TenantStatus.Active);
        }

        Tenant savedTenant = tenantService.save(tenant);
        log.info("Successfully extended subscription for tenant: {} until {}", tenantId, newEndDate);

        return mapToAdminPanelTenantRes(savedTenant);
    }

    @Override
    @Transactional
    public AdminPanelTenantRes disableTenant(String tenantId) {
        log.info("Disabling tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Call domain-manager to suspend domain
        try {
            log.info("Calling domain-manager to suspend domain: {}", tenant.getDomain());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);

            restTemplate.exchange(
                    domainManagerUrl + "/domains/" + tenant.getDomain() + "/suspend",
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("Successfully called domain-manager to suspend domain: {}", tenant.getDomain());
        } catch (Exception e) {
            log.error("Error calling domain-manager to suspend domain {}: {}", tenant.getDomain(), e.getMessage());
            // Continue with database update even if domain-manager call fails
        }

        tenant.setStatus(TenantStatus.Suspended);
        Tenant savedTenant = tenantService.save(tenant);

        log.info("Successfully disabled tenant: {}", tenantId);
        return mapToAdminPanelTenantRes(savedTenant);
    }

    @Override
    @Transactional
    public AdminPanelTenantRes enableTenant(String tenantId) {
        log.info("Enabling tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Call domain-manager to enable domain
        try {
            log.info("Calling domain-manager to enable domain: {}", tenant.getDomain());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);

            restTemplate.exchange(
                    domainManagerUrl + "/domains/" + tenant.getDomain() + "/enable",
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("Successfully called domain-manager to enable domain: {}", tenant.getDomain());
        } catch (Exception e) {
            log.error("Error calling domain-manager to enable domain {}: {}", tenant.getDomain(), e.getMessage());
            // Continue with database update even if domain-manager call fails
        }

        tenant.setStatus(TenantStatus.Active);
        Tenant savedTenant = tenantService.save(tenant);

        log.info("Successfully enabled tenant: {}", tenantId);
        return mapToAdminPanelTenantRes(savedTenant);
    }

    @Override
    @Transactional
    public void deleteTenant(String tenantId) {
        log.info("Deleting tenant: {}", tenantId);

        Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Check if it's main tenant - prevent deletion
        if (tenant.getMain() != null && tenant.getMain()) {
            throw new InvalidParameterException(IdErrorCode.CANNOT_DELETE_DEFAULT);
        }

        // Call domain-manager to suspend domain before deletion
        try {
            log.info("Calling domain-manager to suspend domain before deletion: {}", tenant.getDomain());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);

            restTemplate.exchange(
                    domainManagerUrl + "/domains/" + tenant.getDomain() + "/suspend",
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );
            log.info("Successfully called domain-manager to suspend domain: {}", tenant.getDomain());
        } catch (Exception e) {
            log.error("Error calling domain-manager to suspend domain {}: {}", tenant.getDomain(), e.getMessage());
            // Continue with deletion even if domain-manager call fails
        }

        // Physical deletion as per user preference
        tenantService.delete(tenant.getDomain());

        log.info("Successfully deleted tenant: {}", tenantId);
    }

    @Override
    @Transactional
    public TransactionRes addBalanceToUser(Long userId, AdminBalanceOperationReq request) {
        log.info("Adding balance to user: {}, amount: {}", userId, request.getAmount());

        // Find the user
        GUser user = gUserRepository.findByIdForAdmin(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Add balance using BalanceService
        GUser updatedUser = balanceService.addBalance(
                user,
                request.getAmount(),
                TransactionType.Bonus,
                request.getSource(),
                request.getNote() != null ? request.getNote() : "Added by admin"
        );

        // Create transaction response
        TransactionRes response = new TransactionRes();
        response.setUser(updatedUser.getId());
        response.setChange(request.getAmount());
        response.setBalance(updatedUser.getBalance());
        response.setNote(request.getNote() != null ? request.getNote() : "Added by admin");
        response.setType(TransactionType.Bonus);
        response.setSource(request.getSource());
        response.setCreatedAt(java.time.OffsetDateTime.now());

        log.info("Successfully added balance to user: {}, new balance: {}", userId, updatedUser.getBalance());
        return response;
    }

    @Override
    @Transactional
    public TransactionRes deductBalanceFromUser(Long userId, AdminBalanceOperationReq request) {
        log.info("Deducting balance from user: {}, amount: {}", userId, request.getAmount());

        // Find the user
        GUser user = gUserRepository.findByIdForAdmin(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Deduct balance using BalanceService
        GUser updatedUser = balanceService.deductBalance(
                user,
                request.getAmount(),
                TransactionType.Remove,
                request.getSource(),
                request.getNote() != null ? request.getNote() : "Deducted by admin"
        );

        // Create transaction response
        TransactionRes response = new TransactionRes();
        response.setUser(updatedUser.getId());
        response.setChange(request.getAmount().negate()); // Negative for deduction
        response.setBalance(updatedUser.getBalance());
        response.setNote(request.getNote() != null ? request.getNote() : "Deducted by admin");
        response.setType(TransactionType.Remove);
        response.setSource(request.getSource());
        response.setCreatedAt(java.time.OffsetDateTime.now());

        log.info("Successfully deducted balance from user: {}, new balance: {}", userId, updatedUser.getBalance());
        return response;
    }

    @Override
    public Page<TransactionRes> getUserTransactions(Long userId, int page, int size) {
        log.info("Getting transaction history for user: {}, page: {}, size: {}", userId, page, size);

        // Find the user to verify existence
        GUser user = gUserRepository.findByIdForAdmin(userId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Create pageable
        Pageable pageable = PageRequest.of(page, size);

        // Get transactions for user
        List<GTransaction> transactions = transactionRepository.findTopByUserIdOrderByCreatedAtDesc(userId, pageable);

        // Convert to TransactionRes
        List<TransactionRes> transactionResponses = transactions.stream()
                .map(this::mapToTransactionRes)
                .collect(Collectors.toList());

        // For total count, we need a separate query or use the existing method
        // For now, we'll return the current page size as total (this is a simplification)
        long totalElements = transactions.size();

        return new PageImpl<>(transactionResponses, pageable, totalElements);
    }

    private TransactionRes mapToTransactionRes(GTransaction transaction) {
        TransactionRes response = new TransactionRes();
        response.setUser(transaction.getUserId());
        response.setChange(transaction.getChange());
        response.setBalance(transaction.getBalance());
        response.setNote(transaction.getNote());
        response.setType(transaction.getType());
        response.setSource(transaction.getSource());
        response.setCreatedAt(transaction.getCreatedAt());

        // Set order ID if available
        if (transaction.getOrder() != null) {
            response.setOrderId(transaction.getOrder().getId());
        }

        return response;
    }

}
