package tndung.vnfb.smm.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.dto.request.TenantI18nContentReq;
import tndung.vnfb.smm.dto.response.TenantI18nContentRes;
import tndung.vnfb.smm.entity.Language;
import tndung.vnfb.smm.entity.TenantI18nContent;
import tndung.vnfb.smm.entity.Translation;
import tndung.vnfb.smm.repository.nontenant.LanguageRepository;
import tndung.vnfb.smm.repository.tenant.TenantI18nContentRepository;
import tndung.vnfb.smm.repository.tenant.TranslationRepository;
import tndung.vnfb.smm.service.TenantI18nContentService;
import tndung.vnfb.smm.service.cache.TranslationCacheService;

import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantI18nContentServiceImpl implements TenantI18nContentService {

    private final TranslationRepository translationRepository;
    private final LanguageRepository languageRepository;
    private final TranslationCacheService cacheService;
    private final ObjectMapper objectMapper;
    private final TenantI18nContentRepository tenantI18nContentRepository;

    @Override
    public TenantI18nContentRes getI18nContent(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Getting i18n content for tenant: {} and language: {}", tenantId, languageCode);

        // Try cache first
        Map<String, Object> mergedTranslations = cacheService.getTranslations(tenantId, languageCode);

        if (mergedTranslations == null) {
            // Load template from i18n-template directory
            Map<String, Object> templateTranslations = getTemplateForLanguage(languageCode);

            // Load customized keys from TenantI18nContent table
            List<TenantI18nContent> customizedKeys = tenantI18nContentRepository.findByTenantIdAndLanguageCode(tenantId, languageCode);

            // Merge template with customized keys
            mergedTranslations = mergeTranslations(templateTranslations, customizedKeys);

            // Cache the merged result
            if (mergedTranslations != null) {
                cacheService.putTranslations(tenantId, languageCode, mergedTranslations);
            }
        }

        // Get default template to calculate statistics
        Map<String, Object> defaultTemplate = getTemplateForLanguage(languageCode);
        int totalKeys = flattenMap(defaultTemplate).size();

        // Count customized keys from database
        List<TenantI18nContent> customizedKeysList = tenantI18nContentRepository.findByTenantIdAndLanguageCode(tenantId, languageCode);
        int customizedKeys = customizedKeysList.size();

        // Get last modified time from TenantI18nContent
        OffsetDateTime lastModified = customizedKeysList.stream()
                .map(t -> t.getUpdatedAt())
                .filter(Objects::nonNull)
                .max(OffsetDateTime::compareTo)
                .orElse(null);

        return new TenantI18nContentRes(
                languageCode,
                flattenMap(mergedTranslations),
                lastModified,
                totalKeys,
                customizedKeys
        );
    }

    @Override
    @Transactional
    public TenantI18nContentRes updateI18nContent(String languageCode, TenantI18nContentReq request) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Updating i18n content for tenant: {} and language: {}", tenantId, languageCode);

        // Get template translations to compare
        Map<String, Object> templateTranslations = getTemplateForLanguage(languageCode);
        Map<String, String> flatTemplateTranslations = flattenMap(templateTranslations);
        Map<String, String> flatRequestTranslations = flattenMap(request.getTranslations());

        // Find keys that are different from template (customized keys)
        Set<String> keysToSave = new HashSet<>();
        Set<String> keysToDelete = new HashSet<>();

        for (Map.Entry<String, String> entry : flatRequestTranslations.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            String templateValue = flatTemplateTranslations.get(key);

            // If value is different from template, save it
            if (!Objects.equals(value, templateValue)) {
                keysToSave.add(key);
            }
        }

        // Find keys that exist in template but not in request (should be deleted)
        List<TenantI18nContent> existingCustomizations = tenantI18nContentRepository.findByTenantIdAndLanguageCode(tenantId, languageCode);
        for (TenantI18nContent existing : existingCustomizations) {
            if (!flatRequestTranslations.containsKey(existing.getTranslationKey())) {
                keysToDelete.add(existing.getTranslationKey());
            }
        }

        // Delete keys that should be removed
        for (String keyToDelete : keysToDelete) {
            tenantI18nContentRepository.findByTenantIdAndLanguageCodeAndKey(tenantId, languageCode, keyToDelete)
                    .ifPresent(tenantI18nContentRepository::delete);
        }

        // Save or update customized keys
        for (String keyToSave : keysToSave) {
            String value = flatRequestTranslations.get(keyToSave);

            TenantI18nContent content = tenantI18nContentRepository.findByTenantIdAndLanguageCodeAndKey(tenantId, languageCode, keyToSave)
                    .orElse(new TenantI18nContent());

            content.setLanguageCode(languageCode);
            content.setTranslationKey(keyToSave);
            content.setTranslationValue(value);

            tenantI18nContentRepository.save(content);
        }

        // Clear cache to force reload
        cacheService.evictTranslations(tenantId, languageCode);

        log.info("Updated translations for tenant: {} and language: {} with {} customized keys",
                tenantId, languageCode, keysToSave.size());

        return getI18nContent(languageCode);
    }

    @Override
    public List<String> getAvailableLanguageCodes() {
        String tenantId = TenantContext.getCurrentTenant();
        return translationRepository.findLanguageCodesByTenantId(tenantId);
    }

    @Override
    public List<Language> getAvailableLanguages() {
        String tenantId = TenantContext.getCurrentTenant();
        List<Translation> translations = translationRepository.findByTenantIdWithActiveLanguages(tenantId);
        return translations.stream()
                .map(Translation::getLanguage)
                .collect(Collectors.toList());
    }

    @Override
    public List<Language> getAllSupportedLanguages() {
        return languageRepository.findAllActiveOrderBySortOrder();
    }

    @Override
    public Map<String, Object> getDashboardTranslations(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        if (tenantId == null) {
            tenantId = TenantContext.getSiteTenant();
        }
        log.debug("Getting dashboard translations for tenant: {} and language: {}", tenantId, languageCode);

        if (tenantId == null) {
            log.warn("No tenant context available, returning default template");
            return getTemplateForLanguage(languageCode);
        }

        // Try cache first (lazy loading)
        Map<String, Object> mergedTranslations = cacheService.getTranslations(tenantId, languageCode);

        if (mergedTranslations == null) {
            // Load template from i18n-template directory
            Map<String, Object> templateTranslations = getTemplateForLanguage(languageCode);

            // Load customized keys from TenantI18nContent table
            List<TenantI18nContent> customizedKeys = tenantI18nContentRepository.findByTenantIdAndLanguageCode(tenantId, languageCode);

            // Merge template with customized keys
            mergedTranslations = mergeTranslations(templateTranslations, customizedKeys);

            // Cache the merged result
            if (mergedTranslations != null) {
                cacheService.putTranslations(tenantId, languageCode, mergedTranslations);
            }
        }

        return mergedTranslations != null ? mergedTranslations : getTemplateForLanguage(languageCode);
    }

    @Override
    @Transactional
    public void deleteI18nContent(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        log.debug("Deleting i18n content for tenant: {} and language: {}", tenantId, languageCode);

        // Delete from TenantI18nContent table
        tenantI18nContentRepository.deleteByTenantIdAndLanguageCode(tenantId, languageCode);

        // Clear cache
        cacheService.evictTranslations(tenantId, languageCode);

        log.info("Deleted translations for tenant: {} and language: {}", tenantId, languageCode);
    }

    @Override
    public Map<String, Object> getDefaultTemplate() {
        return getTemplateForLanguage("en");
    }

    /**
     * Get template for specific language, fallback to English if not found
     */
    public Map<String, Object> getTemplateForLanguage(String languageCode) {
        try {
            // Try to get template for specific language first
            ClassPathResource resource = new ClassPathResource("static/i18n-template/" + languageCode + ".json");

            if (!resource.exists()) {
                log.warn("Template for language {} not found, trying English fallback", languageCode);
                resource = new ClassPathResource("static/i18n-template/en.json");
            }

            if (!resource.exists()) {
                log.warn("Default template en.json not found, returning empty map");
                return new HashMap<>();
            }

            TypeReference<Map<String, Object>> typeRef = new TypeReference<Map<String, Object>>() {};
            Map<String, Object> template = objectMapper.readValue(resource.getInputStream(), typeRef);

            log.info("Loaded template for language: {} (file: {})", languageCode, resource.getFilename());
            return template;
        } catch (IOException e) {
            log.error("Error reading template for language: {}", languageCode, e);
            return new HashMap<>();
        }
    }

    @Override
    @Transactional
    public TenantI18nContentRes importTranslations(String languageCode, Map<String, Object> translations) {
        TenantI18nContentReq request = new TenantI18nContentReq();
        request.setLanguageCode(languageCode);
        request.setTranslations(translations);

        return updateI18nContent(languageCode, request);
    }

    @Override
    public void clearCache(String languageCode) {
        String tenantId = TenantContext.getCurrentTenant();
        cacheService.evictTranslations(tenantId, languageCode);
        log.info("Cleared cache for tenant: {} language: {}", tenantId, languageCode);
    }

    @Override
    public void clearAllCache() {
        String tenantId = TenantContext.getCurrentTenant();
        cacheService.evictAllTranslations(tenantId);
        log.info("Cleared all cache for tenant: {}", tenantId);
    }

    @Override
    public void warmUpCache() {
        String tenantId = TenantContext.getCurrentTenant();
        cacheService.warmUpCache(tenantId);
        log.info("Warmed up cache for tenant: {}", tenantId);
    }

    @Override
    public Map<String, Object> getCacheStats() {
        return cacheService.getCacheStats();
    }

    /**
     * Flatten nested map to dot notation keys
     */
    private Map<String, String> flattenMap(Map<String, Object> map) {
        Map<String, String> result = new HashMap<>();
        flattenMapRecursive(map, "", result);
        return result;
    }

    @SuppressWarnings("unchecked")
    private void flattenMapRecursive(Map<String, Object> map, String prefix, Map<String, String> result) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
            Object value = entry.getValue();

            if (value instanceof Map) {
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                flattenMapRecursive(nestedMap, key, result);
            } else if (value != null) {
                result.put(key, value.toString());
            }
        }
    }

    /**
     * Convert flat dot notation keys back to nested structure
     */
    @SuppressWarnings("unchecked")
    private Map<String, Object> unflattenMap(Map<String, String> flatMap) {
        Map<String, Object> result = new HashMap<>();

        for (Map.Entry<String, String> entry : flatMap.entrySet()) {
            String[] keys = entry.getKey().split("\\.");
            Map<String, Object> current = result;

            for (int i = 0; i < keys.length - 1; i++) {
                current = (Map<String, Object>) current.computeIfAbsent(keys[i], k -> new HashMap<String, Object>());
            }

            current.put(keys[keys.length - 1], entry.getValue());
        }

        return result;
    }

    /**
     * Merge template translations with customized keys from TenantI18nContent
     */
    private Map<String, Object> mergeTranslations(Map<String, Object> templateTranslations, List<TenantI18nContent> customizedKeys) {
        // Start with template translations
        Map<String, Object> result = new HashMap<>(templateTranslations);

        // Flatten template to work with dot notation keys
        Map<String, String> flatResult = flattenMap(result);

        // Override with customized values
        for (TenantI18nContent customKey : customizedKeys) {
            flatResult.put(customKey.getTranslationKey(), customKey.getTranslationValue());
        }

        // Convert back to nested structure
        return unflattenMap(flatResult);
    }
}
