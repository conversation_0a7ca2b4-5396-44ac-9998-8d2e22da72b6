package tndung.vnfb.smm.service.impl;

import dev.samstevens.totp.exceptions.QrGenerationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tndung.vnfb.smm.anotation.TenantAccessCheck;
import tndung.vnfb.smm.config.AuditContextHolder;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.enums.CommonStatus;
import tndung.vnfb.smm.dto.TokenPairDto;
import tndung.vnfb.smm.dto.connections.ConnectionSettingsDto;
import tndung.vnfb.smm.dto.request.ForgotPasswordReq;
import tndung.vnfb.smm.dto.request.LoginReq;
import tndung.vnfb.smm.dto.request.MFADisabledReq;
import tndung.vnfb.smm.dto.request.MFALoginReq;
import tndung.vnfb.smm.dto.request.MFAReq;
import tndung.vnfb.smm.dto.request.ResetPasswordReq;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.MFARes;
import tndung.vnfb.smm.dto.response.TenantInfoDto;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.KeyToken;
import tndung.vnfb.smm.entity.PasswordResetToken;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.exception.MfaRequiredException;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.mapper.GUserMapper;
import tndung.vnfb.smm.mapper.TenantMapper;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.nontenant.UserTenantRepository;
import tndung.vnfb.smm.repository.nontenant.PasswordResetTokenRepository;
import tndung.vnfb.smm.service.*;
import tndung.vnfb.smm.service.cache.MFACachedService;
import tndung.vnfb.smm.util.IpAddressUtil;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AccessServiceImpl implements AccessService {

    @Value("${jwt.header.refresh-token}")
    public String refreshTokenKey;

    @Value("${jwt.header.client-id}")
    public String clientIdKey;
    // private final TimezoneUtil timezoneUtil;
    private final AuthenticationManager authenticationManager;
    private final AuthenticationFacade authenticationFacade;
    private final GUserMapper userMapper;
    private final TokenProvider tokenProvider;
    private final KeyTokenService keyTokenService;

    private final GUserRepository gUserRepository;
    private final HttpServletRequest request;
    private final MFACachedService mfaCachedService;
    private final MFATokenManager mfaTokenManager;
    private final BCryptPasswordEncoder bcryptEncoder;
    private final LoginHistoryService loginHistoryService;
    private final UserTenantAccessService userTenantAccessService;
    private final TenantService tenantService;
    private final UserTenantService userTenantService;
    private final TenantMapper tenantMapper;
    private final UserTenantRepository userTenantRepository;
    private final PasswordResetTokenRepository passwordResetTokenRepository;
    private final EmailService emailService;
    private final LoginSecurityService loginSecurityService;
    private final ConnectionSettingsServiceImpl connectionSettingsService;

    @Override
    public void disableMFA(MFADisabledReq req) {
        final GUser user = getCurrentUser();
        if (Boolean.FALSE.equals(user.getMfaEnabled())) throw new InvalidParameterException(IdErrorCode.MFA_IS_OFF);
        if (!bcryptEncoder.matches(req.getPassword(), user.getPassword()))
            throw new InvalidParameterException(IdErrorCode.PASSWORD_WRONG);
        user.setMfaEnabled(false);
        user.setSecretKey(null);
        gUserRepository.save(user);
    }

    @Override
    public void verifyMFA(MFAReq req) {


        final GUser user = getCurrentUser();
        if (Boolean.TRUE.equals(user.getMfaEnabled())) throw new InvalidParameterException(IdErrorCode.MFA_IS_ON);

        if (!bcryptEncoder.matches(req.getPassword(), user.getPassword()))
            throw new InvalidParameterException(IdErrorCode.PASSWORD_WRONG);

        if (!mfaTokenManager.verifyTotp(req.getCode(), req.getSecretKey())) {
            throw new InvalidParameterException(IdErrorCode.OTP_INCORRECT);
        }
        user.setMfaEnabled(true);
        user.setSecretKey(req.getSecretKey());
        gUserRepository.save(user);

    }

    @Override
    public MFARes generateMFA() throws QrGenerationException {
        final String secretKey = mfaTokenManager.generateSecretKey();
        return MFARes.builder()
                .secretKey(secretKey)
                .image(mfaTokenManager.getQRCode(secretKey))
                .build();
    }

    /**
     * Authenticate a user with MFA (Multi-Factor Authentication)
     *
     * @param loginReq MFA login request containing username, password, code and login first factor
     * @return User response with tokens
     * @throws MfaRequiredException if MFA validation fails
     */
    @Override
    @Transactional
    public GUserRes mfa(MFALoginReq loginReq) throws MfaRequiredException {
        log.info("MFA login attempt for user: {}", loginReq.getUserName());

        // Validate MFA session
        validateMfaSession(loginReq);

        // Authenticate user credentials
        GUser user = authenticateUser(loginReq.getUserName(), loginReq.getPassword());

        // Verify MFA code
        if (!mfaTokenManager.verifyTotp(loginReq.getCode(), user.getSecretKey())) {
            log.warn("Invalid MFA code provided for user: {}", loginReq.getUserName());
            throw new InvalidParameterException(IdErrorCode.OTP_INCORRECT);
        }

        // Complete login process
        GUserRes userRes = completeLoginProcess(user);

        // Clean up MFA session
        mfaCachedService.evict(loginReq.getLoginFirstFactor());

        log.info("MFA login successful for user: {}", loginReq.getUserName());
        return userRes;
    }

    /**
     * Get the current authenticated user
     *
     * @return Current user entity
     * @throws UsernameNotFoundException if user is not found
     */
    private GUser getCurrentUser() {
        return authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new UsernameNotFoundException("User not found"));
    }

    /**
     * Regular login process
     *
     * @param loginReq Login request containing username and password
     * @return User response with tokens
     * @throws MfaRequiredException if MFA is required for this user
     */
    @Override
    @Transactional(noRollbackFor = MfaRequiredException.class)
    public GUserRes login(LoginReq loginReq) {
        log.info("Login attempt for user: {}", loginReq.getUserName());

        // Authenticate user credentials
        GUser user = authenticateUser(loginReq.getUserName(), loginReq.getPassword());

        // Set user timezone
        AuditContextHolder.setUserZone(ZoneOffset.of(user.getTimeZone()));

        // Check if MFA is required
        checkMfaRequired(user, loginReq.getUserName());

        // Complete login process
        GUserRes userRes = completeLoginProcess(user);

        log.info("Login successful for user: {}", loginReq.getUserName());
        return userRes;
    }

    /**
     * Validate MFA session from login first factor
     *
     * @param loginReq MFA login request
     * @throws InvalidParameterException if session is invalid
     */
    private void validateMfaSession(MFALoginReq loginReq) {
        final String userName = mfaCachedService.get(loginReq.getLoginFirstFactor());
        if (Strings.isBlank(userName) || !userName.equals(loginReq.getUserName())) {
            log.warn("Invalid MFA session for user: {}", loginReq.getUserName());
            throw new InvalidParameterException(IdErrorCode.SESSION_INCORRECT);
        }
    }

    /**
     * Authenticate user with username and password
     *
     * @param username Username
     * @param password Password
     * @return Authenticated user
     * @throws InvalidParameterException if user account is deactivated
     */
    private GUser authenticateUser(String username, String password) {
        final Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(username, password)
        );

        SecurityContextHolder.getContext().setAuthentication(authentication);
        GUser user = getCurrentUser();

        // Check if user account is deactivated
        if (user.getStatus() == CommonStatus.DEACTIVATED) {
            log.warn("Login attempt for deactivated user: {}", username);
            throw new InvalidParameterException(IdErrorCode.USER_ACCOUNT_DEACTIVATED);
        }

        return user;
    }

    /**
     * Check if MFA is required for the user
     *
     * @param user     User entity
     * @param username Username
     * @throws MfaRequiredException if MFA is required
     */
    private void checkMfaRequired(GUser user, String username) {
        if (Boolean.TRUE.equals(user.getMfaEnabled()) && !Strings.isBlank(user.getSecretKey())) {
            log.info("MFA required for user: {}", username);
            final String loginFirstFactor = CommonHelper.generateRandomText(36);
            mfaCachedService.put(loginFirstFactor, username);
            throw new MfaRequiredException(loginFirstFactor);
        }
    }

    /**
     * Complete the login process by generating tokens and updating user data
     *
     * @param user Authenticated user
     * @return User response with tokens
     */
    private GUserRes completeLoginProcess(GUser user) {
        final TokenPairDto tokenPairDto;
        if (user.isPanel()) {
            tokenPairDto = keyTokenService.createKeyPairPanel(user, null);
        } else if (user.isChildPanel() ) {
            tokenPairDto = keyTokenService.createKeyPairPanel(user, null);
        }

        else if (user.isUser()) {
            tokenPairDto = keyTokenService.createKeyPairUser(user);
        } else {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }
        // Generate tokens


        // Create user response
        final GUserRes userRes = userMapper.toRes(user);
        userRes.setTokens(tokenPairDto);

        // Update last login time
        user.setLastLoginAt(OffsetDateTime.now(AuditContextHolder.getUserZone()));
        gUserRepository.save(user);



        // Check for unusual login and send notification if needed
        String currentIp = IpAddressUtil.getClientIpAddress(request);
        String userAgent = IpAddressUtil.getUserAgent(request);
        loginSecurityService.checkAndNotifyUnusualLogin(user, currentIp, userAgent);

        // Save login history
        loginHistoryService.save();



        return userRes;
    }


    @Override
    @Transactional
    public GUserRes refresh() {

        final String clientId = request.getHeader(clientIdKey);
        final KeyToken keyToken = keyTokenService.findByIdAndSite(clientId);
        final String refreshToken = request.getHeader(refreshTokenKey);


        final GUser user = gUserRepository.findById(keyToken.getUserId()).
                orElseThrow(() -> new InvalidParameterException(IdErrorCode.INVALID_TOKEN));
        if (Strings.isEmpty(refreshToken))
            throw new InvalidParameterException(IdErrorCode.INVALID_TOKEN);
        if (!tokenProvider.validateToken(keyToken.getPrivateKey(), refreshToken, user.getUserName()))
            throw new InvalidParameterException(IdErrorCode.INVALID_TOKEN);


        if (keyToken.getUsedRefreshTokens() == null)
            keyToken.setUsedRefreshTokens(new ArrayList<>());
        if (!CollectionUtils.isEmpty(keyToken.getUsedRefreshTokens())
                && keyToken.getUsedRefreshTokens().contains(refreshToken)) {
            keyTokenService.deleteById(keyToken.getId());
            throw new InvalidParameterException(IdErrorCode.INVALID_TOKEN);
        }

        if (!keyToken.getRefreshToken().equals(refreshToken)) {
            throw new InvalidParameterException(IdErrorCode.INVALID_TOKEN);
        }


        final TokenPairDto tokenPairDto = keyTokenService.createKeyPairPanel(user, null);

        keyToken.getUsedRefreshTokens().add(refreshToken);
        keyToken.setRefreshToken(refreshToken);
        keyTokenService.save(keyToken);

        GUserRes res = userMapper.toRes(user);
        res.setTokens(tokenPairDto);

        return res;
    }

    @Override
    public void logout() {
        authenticationFacade.getKeyToken().ifPresent(keyToken ->
                keyTokenService.deleteById(keyToken.getId())
        );
    }

    @Override
    @Transactional
    @TenantAccessCheck
    public TokenPairDto switchTenant(String tenantId) {
        // Get current user information
        GUser user = getCurrentUser();

        // Generate new token with the selected tenant
        return keyTokenService.createKeyPairPanel(user, tenantId);
    }

    @Override
    public List<TenantInfoDto> getAccessibleTenants() {
        // Get current user information
        GUser user = getCurrentUser();

        // Get list of accessible tenant IDs for the user
        List<String> tenantIds = userTenantAccessService.getAccessibleTenantIds(user.getId());

        // Convert tenant IDs to TenantInfoDto objects with domain information
        return tenantIds.stream()
                .map(id -> {
                    // Find tenant by ID to get domain information
                    return tenantService.findByTenantId(id)
                            .map(tenantMapper::toRes)
                            .orElse(new TenantInfoDto(id)); // Fallback if tenant not found
                })
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public TokenPairDto createTokenForUser(Long userId) {


        // Get current tenant from context
        String currentTenant = TenantContext.getCurrentTenant();

        log.info("Creating token for user: {} with tenant: {}", userId, currentTenant);
        if (currentTenant == null) {
            log.warn("No current tenant in context when creating token for user: {}", userId);
            throw new InvalidParameterException(IdErrorCode.PANEL_NOT_CONFIGURED);
        }

        // Check if user belongs to current tenant
        boolean userBelongsToTenant = userTenantRepository.existsByUserIdAndTenantId(userId, currentTenant);
        if (!userBelongsToTenant) {
            log.warn("User {} does not belong to current tenant: {}", userId, currentTenant);
            throw new InvalidParameterException(IdErrorCode.TENANT_ACCESS_DENIED);
        }

        // Find the user
        GUser user = gUserRepository.findById(userId)
                .orElseThrow(() -> {
                    log.warn("User not found: {}", userId);
                    return new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
                });

        // If tenantId is not provided, use the current tenant

        // If tenantId is provided, verify user has access to it
        boolean hasAccess = userTenantService.hasAccessToTenant(userId, currentTenant);
        if (!hasAccess) {
            log.warn("User {} does not have access to tenant: {}", userId, currentTenant);
            throw new InvalidParameterException(IdErrorCode.TENANT_ACCESS_DENIED);
        }
        // Create token for the user
        TokenPairDto tokenPair = keyTokenService.createKeyPairUserOtherTenant(user);

        log.info("Successfully created token for user: {} with tenant: {}", userId, currentTenant);
        return tokenPair;
    }

    @Override
    @Transactional
    public void forgotPassword(ForgotPasswordReq forgotPasswordReq) {
        String tenantId = TenantContext.getSiteTenant();
        log.info("Forgot password request for email: {} in tenant: {}", forgotPasswordReq.getEmail(), tenantId);

        // Find user by email
        GUser user = gUserRepository.findByEmail(forgotPasswordReq.getEmail())
                .orElse(null);
        final Tenant tenant = tenantService.findByTenantId(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Always return success to prevent email enumeration attacks
        if (user == null) {
            log.warn("Forgot password request for non-existent email: {}", forgotPasswordReq.getEmail());
            return;
        }

        // Check if user account is active
        if (user.getStatus() == CommonStatus.DEACTIVATED) {
            log.warn("Forgot password request for deactivated user: {}", forgotPasswordReq.getEmail());
            return;
        }

        // Delete any existing unused tokens for this email
        passwordResetTokenRepository.deleteUnusedTokensByEmailAndTenantId(
                forgotPasswordReq.getEmail(), tenantId);

        // Generate reset token
        String resetToken = UUID.randomUUID().toString();
        OffsetDateTime expiresAt = OffsetDateTime.now().plusHours(1); // Token expires in 1 hour

        // Save reset token
        PasswordResetToken passwordResetToken = PasswordResetToken.builder()

                .token(resetToken)
                .email(forgotPasswordReq.getEmail())
                .user(user)
                .expiresAt(expiresAt)
                .used(false)
                .build();
        final ConnectionSettingsDto connectionSettingsDto = connectionSettingsService.getSettingsById(tenantId);


        passwordResetTokenRepository.save(passwordResetToken);

        // Send reset email
        try {
            Map<String, String> variables = new HashMap<>();
            variables.put("username", user.getUserName());
            variables.put("email", user.getEmail());
            variables.put("reset_link", generateResetLink(tenant,resetToken));
            variables.put("expiry_time", CommonHelper.formatTimeToNotification(expiresAt, user.getUserZone()));
            variables.put("token", expiresAt.toString());
            variables.put("domain", tenant.getDomain());
            variables.put("support_email", connectionSettingsDto.getSmtp().getSmtpEmail());

            emailService.sendTemplateEmail(tenantId, user.getEmail(), "password_reset", variables);
            log.info("Password reset email sent successfully to: {}", forgotPasswordReq.getEmail());
        } catch (Exception e) {
            log.error("Failed to send password reset email to: {}", forgotPasswordReq.getEmail(), e);
            // Don't throw exception to prevent revealing email existence
        }
    }

    @Override
    @Transactional
    public void resetPassword(ResetPasswordReq resetPasswordReq) {
        String tenantId = TenantContext.getSiteTenant();
        log.info("Reset password request with token in tenant: {}", tenantId);

        // Validate passwords match
        if (!resetPasswordReq.getNewPassword().equals(resetPasswordReq.getConfirmPassword())) {
            throw new InvalidParameterException(IdErrorCode.PASSWORD_MISMATCH);
        }

        // Find and validate token
        PasswordResetToken resetToken = passwordResetTokenRepository
                .findByTokenAndTenantId(resetPasswordReq.getToken(), tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.INVALID_RESET_TOKEN));

        // Check if token is valid
        if (!resetToken.isValid()) {
            throw new InvalidParameterException(IdErrorCode.INVALID_RESET_TOKEN);
        }

        // Get user and update password
        GUser user = resetToken.getUser();
        user.setPassword(bcryptEncoder.encode(resetPasswordReq.getNewPassword()));
        gUserRepository.save(user);

        // Mark token as used
        passwordResetTokenRepository.markTokenAsUsed(
                resetPasswordReq.getToken(), tenantId, LocalDateTime.now());

        // Logout all sessions for this user
        keyTokenService.findByUserIdForUserSite(user.getId())
                .forEach(keyToken -> keyTokenService.deleteById(keyToken.getId()));

        log.info("Password reset successful for user: {}", user.getEmail());
    }

    private String generateResetLink(Tenant tenant, String token) {
        // This should be configurable based on tenant domain
        return String.format("https://%s/auth/reset-password?token=%s",tenant.getDomain(), token);
    }
}
