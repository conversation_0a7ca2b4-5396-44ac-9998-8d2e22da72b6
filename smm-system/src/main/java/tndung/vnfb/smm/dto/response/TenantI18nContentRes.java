package tndung.vnfb.smm.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.OffsetDateTime;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TenantI18nContentRes {
    private String languageCode;
    private Map<String, String> translations;
    private OffsetDateTime lastModified;
    private Integer totalKeys;
    private Integer customizedKeys;
}
