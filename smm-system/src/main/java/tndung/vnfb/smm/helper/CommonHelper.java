package tndung.vnfb.smm.helper;

import lombok.SneakyThrows;
import lombok.experimental.UtilityClass;
import org.apache.logging.log4j.util.Strings;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.UrlResource;
import org.springframework.util.CollectionUtils;
import tndung.vnfb.smm.entity.GService;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.sql.Timestamp;
import java.text.NumberFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static tndung.vnfb.smm.constant.Common.SCALE_DEFAULT;


@UtilityClass
public class CommonHelper {
    private static final Random rnd = new Random();
    private static final String CHARACTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    private static final int GROUP_LENGTH = 4;
    private static final int GROUP_COUNT = 3;
    private static final SecureRandom random = new SecureRandom();

    public static OffsetDateTime getStartDay(LocalDate start, ZoneId timeZone) {
        if (start == null) return null;
        return start.atStartOfDay(timeZone).toOffsetDateTime();
    }

    public static BigDecimal discount(BigDecimal value, BigDecimal discountPercent) {
  // giá gốc
        BigDecimal discountAmount = value
                .multiply(discountPercent)
                .divide(new BigDecimal("100"), SCALE_DEFAULT, RoundingMode.HALF_UP);

        return value.subtract(discountAmount);
    }

    public static BigDecimal extend(BigDecimal value, BigDecimal discountPercent) {

        BigDecimal discountAmount = value
                .multiply(discountPercent)
                .divide(new BigDecimal("100"), SCALE_DEFAULT, RoundingMode.HALF_UP);

        return value.add(discountAmount);
    }
    public static boolean isNumeric(String str) {
        // Only accept positive integers for order ID matching (no decimals, no negative numbers)
        return str != null && str.matches("\\d+");
    }
    public static OffsetDateTime getEndDay(LocalDate end, ZoneId timeZone) {
        if (end == null) return null;
        return end.atStartOfDay(timeZone).plusDays(1).minusSeconds(1).toOffsetDateTime();
    }


    public static String getServiceDetail(GService service) {
        return service.getId() + " - " + service.getName();
    }
    public static String maskEndCharacters(String str, int notMaskCount) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        int length = str.length();
        // Start masking from the 6th character
        return // Keep first five characters
                str.substring(0, Math.min(length, notMaskCount)) + "*".repeat(Math.max(0, length - notMaskCount));
    }
    public static Integer convertToTotalMembers(String text) {

        if (Strings.isBlank(text)) return null;
        Pattern regex = Pattern.compile("\\d{1,3}(,\\d{3})*");
        Matcher matcher = regex.matcher(text);
        if (matcher.find()) {
            String matchedNumber = matcher.group();
            return Integer.parseInt(matchedNumber.replace(",", ""));
        }
        return null;

    }

    public String getRandomName(Integer length) {
        byte[] array = new byte[length]; // length is bounded by 7
        rnd.nextBytes(array);
        return new String(array, StandardCharsets.UTF_8);
    }

    @SneakyThrows
    public ByteArrayResource getByteArrayResource(byte[] bytes) {
        return new ByteArrayResource(bytes) {
            @Override
            public String getFilename() {
                return getRandomName(13) + ".jpg";
            }
        };
    }
    public static String generateNewToken() {
        Supplier<String> tokenSupplier = () -> {

            StringBuilder token = new StringBuilder();
            long currentTimeInMillisecond = Instant.now().toEpochMilli();
            return token.append(currentTimeInMillisecond).append("-").append(UUID.randomUUID()).toString();
        };
        return tokenSupplier.get();
    }

    public static String formatTimeToNotification(OffsetDateTime time, ZoneId zoneId) {
        if (time == null) return null;
        return time.atZoneSameInstant(zoneId).format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));
    }


    public static String generateCode() {
        StringBuilder code = new StringBuilder();

        for (int i = 0; i < GROUP_COUNT; i++) {
            if (i > 0) {
                code.append("-");
            }
            for (int j = 0; j < GROUP_LENGTH; j++) {
                int index = random.nextInt(CHARACTERS.length());
                code.append(CHARACTERS.charAt(index));
            }
        }

        return code.toString();
    }
    public static String generateRandomText(int length) {
        final String CHARACTERS = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuilder sb = new StringBuilder(length);

        for (int i = 0; i < length; i++) {
            int randomIndex = random.nextInt(CHARACTERS.length());
            char randomChar = CHARACTERS.charAt(randomIndex);
            sb.append(randomChar);
        }

        return sb.toString();
    }
    @SneakyThrows
    public UrlResource getUrlResource(String url) {
        return new UrlResource(url) { //uses URL#inputStream
            @Override
            public String getFilename() {
                return getRandomName(10) + ".jpg";
            }
        };
    }


    public String escapeJson(String raw) {
        String escaped = raw;
        escaped = escaped.replace("\\", "\\\\");
        escaped = escaped.replace("\"", "\\\"");
        escaped = escaped.replace("\b", "\\b");
        escaped = escaped.replace("\f", "\\f");
        escaped = escaped.replace("\n", "\\n");
        escaped = escaped.replace("\r", "\\r");
        escaped = escaped.replace("\t", "\\t");
        return escaped;
    }

    public static LocalDateTime toExpired(Integer numberOfDays) {
        if (numberOfDays == null) return null;
        return LocalDateTime.now().plusDays(numberOfDays);

    }

    public static Double convertRateUSD(Double price) {
        double rs = (price * 1000) / 24000;

        rs = Math.ceil(rs * 1000);
        rs = rs / 1000;
        return rs;
    }

    public static Double convertUSD(Double price) {
        double rs = price  / 24000;

        rs = Math.ceil(rs * 1000000);
        rs = rs / 1000000;
        return rs;
    }

    public static List<String> toList(String value, String breakCharacter) {
        if (value == null) return null;

        return Arrays.stream(value.split(breakCharacter))
                .filter(v -> !Strings.isBlank(v)).collect(Collectors.toList());
    }

    public static Set<String> toSet(String value, String breakCharacter) {
        if (value == null) return null;

        return Arrays.stream(value.split(breakCharacter))
                .filter(v -> !Strings.isBlank(v)).collect(Collectors.toSet());
    }

    public static int doubleToSecond(double minute) {
        return (int) minute * 60;
    }


    public static <T> T getRandomElement(List<T> list) {
        if (CollectionUtils.isEmpty(list)) return null;
        return list.get(rnd.nextInt(list.size()));
    }


    public static <T> T getRandomElement(T[] list) {
        if (list == null || list.length == 0) return null;
        return list[rnd.nextInt(list.length)];
    }

    /**
     * Convert LocalDate to OffsetDateTime cho start date
     * Logic: Lấy 00:00:00 của ngày theo timezone gốc, sau đó convert sang UTC
     */
    public static OffsetDateTime convertStartDate(LocalDate startDate, ZoneId zoneId) {
        if (startDate == null) return null;
        // Tạo LocalDateTime với thời gian 00:00:00
        LocalDateTime startLocalDateTime = startDate.atStartOfDay();

        // Convert sang ZonedDateTime với timezone gốc
        ZonedDateTime startZonedDateTime = startLocalDateTime.atZone(zoneId);

        // Convert sang UTC (offset +0)

        return startZonedDateTime.withZoneSameInstant(ZoneOffset.UTC)
                .toOffsetDateTime();
    }

    /**
     * Convert LocalDate to OffsetDateTime cho end date
     * Logic: Lấy 23:59:59 của ngày theo timezone gốc, sau đó convert sang UTC
     */
    public static OffsetDateTime convertEndDate(LocalDate endDate, ZoneId zoneId) {
        if (endDate == null) return null;


        // Tạo LocalDateTime với thời gian 23:59:59
        LocalDateTime endLocalDateTime = endDate.atTime(23, 59, 59);

        // Convert sang ZonedDateTime với timezone gốc
        ZonedDateTime endZonedDateTime = endLocalDateTime.atZone(zoneId);

        // Convert sang UTC (offset +0)

        return endZonedDateTime.withZoneSameInstant(ZoneOffset.UTC)
                .toOffsetDateTime();
    }

    public static OffsetDateTime convertToOffsetDateTimeWithTimezone(Object timestampObj, String timezoneId) {
        ZoneId zoneId = ZoneId.of(timezoneId); // Ví dụ: "Asia/Ho_Chi_Minh"

        if (timestampObj instanceof Timestamp) {
            Timestamp timestamp = (Timestamp) timestampObj;
            return timestamp.toInstant().atOffset(zoneId.getRules().getOffset(timestamp.toInstant()));
        } else if (timestampObj instanceof OffsetDateTime) {
            return (OffsetDateTime) timestampObj;
        } else if (timestampObj instanceof LocalDateTime) {
            LocalDateTime localDateTime = (LocalDateTime) timestampObj;
            return localDateTime.atZone(zoneId).toOffsetDateTime();
        } else {
            throw new IllegalArgumentException("Cannot convert " + timestampObj.getClass() + " to OffsetDateTime");
        }
    }


    public static String formatPriceNotification(BigDecimal price) {
        NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.US);
        currencyFormat.setRoundingMode(RoundingMode.HALF_UP);
        currencyFormat.setMaximumFractionDigits(6);
        currencyFormat.setMinimumFractionDigits(0);  // Không bắt buộc thêm số 0
       return currencyFormat.format(price);

    }
}
