import { Injectable, ElementRef } from '@angular/core';
import { IconName } from '@fortawesome/fontawesome-svg-core';
import { AdminDropdownService, DropdownOptions } from './admin-dropdown.service';

export interface MenuAction {
  id: string;
  label: string;
  icon: IconName;
  iconColor: string;
  divider?: boolean;
  disabled?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class AdminMenuService {
  // User menu actions
  private userMenuActions: MenuAction[] = [
    { id: 'manage-balance', label: 'menu.user.manage_balance', icon: 'wallet', iconColor: 'text-blue-500' },
    { id: 'edit-user', label: 'menu.user.edit_user', icon: 'pen', iconColor: 'text-blue-500' },
    { id: 'user-orders', label: 'menu.user.user_orders', icon: 'shopping-cart', iconColor: 'text-blue-500' },
    { id: 'payments-history', label: 'menu.user.payments_history', icon: 'history', iconColor: 'text-blue-500' },
    { id: 'referral-system', label: 'menu.user.referral_system', icon: 'users', iconColor: 'text-blue-500' },
    { id: 'discount-prices', label: 'menu.user.discount_prices', icon: 'tag', iconColor: 'text-blue-500' },
    { id: 'reset-password', label: 'menu.user.reset_password', icon: 'key', iconColor: 'text-blue-500' },
    { id: 'login-as-user', label: 'menu.user.login_as_user', icon: 'sign-in-alt', iconColor: 'text-blue-500' },
    { id: 'ban-account', label: 'menu.user.ban_account', icon: 'ban', iconColor: 'text-red-500' }
  ];

  // Order menu actions
  private orderMenuActions: MenuAction[] = [
    { id: 'mark-completed', label: 'menu.order.mark_completed', icon: 'check', iconColor: 'text-green-500' },
    { id: 'resend-provider', label: 'menu.order.resend_provider', icon: 'paper-plane', iconColor: 'text-blue-500' },
    { id: 'set-start-count', label: 'menu.order.set_start_count', icon: 'play', iconColor: 'text-blue-500' },
    { id: 'edit-order-link', label: 'menu.order.edit_order_link', icon: 'pen', iconColor: 'text-blue-500' },
    { id: 'cancel-partially', label: 'menu.order.cancel_partially', icon: 'ban', iconColor: 'text-red-500' },
    { id: 'cancel-refund', label: 'menu.order.cancel_refund', icon: 'times-circle', iconColor: 'text-red-500' }
  ];

  // Support ticket menu actions
  private ticketMenuActions: MenuAction[] = [
    { id: 'view-details', label: 'menu.ticket.view_details', icon: 'eye', iconColor: 'text-blue-500' },
    { id: 'mark-accepted', label: 'menu.ticket.mark_accepted', icon: 'check', iconColor: 'text-green-500' },
    { id: 'mark-solved', label: 'menu.ticket.mark_solved', icon: 'check-circle', iconColor: 'text-green-500' },
    { id: 'mark-closed', label: 'menu.ticket.mark_closed', icon: 'times-circle', iconColor: 'text-red-500' }
  ];

  // Service menu actions
  private serviceMenuActions: MenuAction[] = [
    { id: 'edit-service', label: 'menu.service.edit_service', icon: 'pen', iconColor: 'text-blue-500' },
    { id: 'duplicate-service', label: 'menu.service.duplicate_service', icon: 'copy', iconColor: 'text-blue-500' },
    { id: 'toggle-status', label: 'menu.service.toggle_status', icon: 'toggle-on', iconColor: 'text-blue-500' },
    { id: 'delete-service', label: 'menu.service.delete_service', icon: 'trash', iconColor: 'text-red-500' }
  ];

  constructor(private adminDropdownService: AdminDropdownService) { }

  getUserMenuActions(): MenuAction[] {
    return this.userMenuActions;
  }

  getOrderMenuActions(): MenuAction[] {
    return this.orderMenuActions;
  }

  getTicketMenuActions(): MenuAction[] {
    return this.ticketMenuActions;
  }

  getServiceMenuActions(): MenuAction[] {
    return this.serviceMenuActions;
  }

  // Calculate menu position based on button element using the AdminDropdownService
  calculateMenuPosition(
    triggerElement: ElementRef | HTMLElement,
    dropdownElement: HTMLElement,
    options: DropdownOptions = {}
  ): { top: number, left: number } {
    // Default options for admin menus
    const defaultOptions: DropdownOptions = {
      placement: 'bottom-right',
      offset: { x: 0, y: 5 },
      width: '224px',
      maxHeight: '400px'
    };

    // Merge default options with provided options
    const mergedOptions = { ...defaultOptions, ...options };

    return this.adminDropdownService.calculatePosition(triggerElement, dropdownElement, mergedOptions);
  }
}
