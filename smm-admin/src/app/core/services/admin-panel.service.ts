import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap, map, catchError } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { AdminPanelTenant } from '../../model/response/admin-panel-tenant.model';
import { AdminPanelUser } from '../../model/response/admin-panel-user.model';
import { AdminPanelUserRequest } from '../../model/request/admin-panel-user.model';
import { AdminBalanceOperationReq } from '../../model/request/admin-balance-operation.model';
import { TransactionRes } from '../../model/response/transaction.model';

export interface PageResponse<T> {
  content: T[];
  total_elements: number;
  total_pages: number;
  page_size: number;
  page_number: number;
}

// Response structure for orders
export interface OrderPageResponse<T> {
  content: T[];
  total_elements: number;
  total_pages: number;
  pageable: {
    page_number: number;
    page_size: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class AdminPanelService {
  private readonly baseUrl: string;

  // State management
  private panelsSubject = new BehaviorSubject<AdminPanelTenant[]>([]);
  private usersSubject = new BehaviorSubject<AdminPanelUser[]>([]);
  private loadingSubject = new BehaviorSubject<boolean>(false);
  private panelsPaginationSubject = new BehaviorSubject<any>({
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  });
  private usersPaginationSubject = new BehaviorSubject<any>({
    page_number: 0,
    page_size: 10,
    total_elements: 0,
    total_pages: 0
  });

  // Observables
  public panels$ = this.panelsSubject.asObservable();
  public users$ = this.usersSubject.asObservable();
  public loading$ = this.loadingSubject.asObservable();
  public panelsPagination$ = this.panelsPaginationSubject.asObservable();
  public usersPagination$ = this.usersPaginationSubject.asObservable();

  constructor(
    private http: HttpClient,
    private configService: ConfigService
  ) {
    this.baseUrl = `${this.configService.apiUrl}/admin-panels`;
  }

  /**
   * Get all panels (tenants with main = false)
   */
  getAllPanels(page: number = 0, size: number = 10, search?: string): Observable<PageResponse<AdminPanelTenant>> {
    this.loadingSubject.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    return this.http.get<PageResponse<AdminPanelTenant>>(`${this.baseUrl}/panels`, { params })
      .pipe(
        tap(response => {
          this.panelsSubject.next(response.content);
          this.panelsPaginationSubject.next({
            page_number: response.page_number,
            page_size: response.page_size,
            total_elements: response.total_elements,
            total_pages: response.total_pages
          });
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Get all users from main tenant
   */
  getMainTenantUsers(page: number = 0, size: number = 10, search?: string): Observable<PageResponse<AdminPanelUser>> {
    this.loadingSubject.next(true);

    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    if (search && search.trim()) {
      params = params.set('search', search.trim());
    }

    return this.http.get<PageResponse<AdminPanelUser>>(`${this.baseUrl}/main-tenant-users`, { params })
      .pipe(
        tap(response => {
          this.usersSubject.next(response.content);
          this.usersPaginationSubject.next({
            page_number: response.page_number,
            page_size: response.page_size,
            total_elements: response.total_elements,
            total_pages: response.total_pages
          });
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Add balance to user
   */
  addBalanceToUser(userId: number, request: AdminBalanceOperationReq): Observable<TransactionRes> {
    return this.http.post<TransactionRes>(`${this.baseUrl}/users/${userId}/add-balance`, request)
      .pipe(
        map(response => response)
      );
  }

  /**
   * Deduct balance from user
   */
  deductBalanceFromUser(userId: number, request: AdminBalanceOperationReq): Observable<TransactionRes> {
    return this.http.post<TransactionRes>(`${this.baseUrl}/users/${userId}/deduct-balance`, request)
      .pipe(
        map(response => response)
      );
  }

  /**
   * Get user transaction history
   */
  getUserTransactions(userId: number, page: number = 0, size: number = 10): Observable<PageResponse<TransactionRes>> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('size', size.toString());

    return this.http.get<PageResponse<TransactionRes>>(`${this.baseUrl}/users/${userId}/transactions`, { params })
      .pipe(
        map(response => {
          // Convert Spring Page to our PageResponse format
          const springPage = response;
          return {
            content: springPage.content,
            total_elements: springPage.total_elements,
            total_pages: springPage.total_pages,
            page_size: springPage.page_size,
            page_number: springPage.page_number
          };
        })
      );
  }

  /**
   * Add money to a main tenant user
   */
  addMoneyToUser(request: AdminPanelUserRequest): Observable<TransactionRes> {
    this.loadingSubject.next(true);

    return this.http.post<TransactionRes>(`${this.baseUrl}/add-money`, request)
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
        })
      );
  }

  /**
   * Get tenant details by ID
   */
  getTenantById(tenantId: string): Observable<AdminPanelTenant> {
    return this.http.get<AdminPanelTenant>(`${this.baseUrl}/panels/${tenantId}`);
  }

  /**
   * Get user details by ID
   */
  getUserById(userId: number): Observable<AdminPanelUser> {
    return this.http.get<AdminPanelUser>(`${this.baseUrl}/users/${userId}`);
  }

  /**
   * Extend tenant subscription
   */
  extendTenantSubscription(tenantId: string, extensionDays: number): Observable<AdminPanelTenant> {
    this.loadingSubject.next(true);

    const request = { extension_days: extensionDays };

    return this.http.put<AdminPanelTenant>(`${this.baseUrl}/panels/${tenantId}/extend-subscription`, request)
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
          // Refresh panels list
          this.getAllPanels().subscribe();
        })
      );
  }

  /**
   * Disable tenant
   */
  disableTenant(tenantId: string): Observable<AdminPanelTenant> {
    this.loadingSubject.next(true);

    return this.http.put<AdminPanelTenant>(`${this.baseUrl}/panels/${tenantId}/disable`, {})
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
          // Refresh panels list
          this.getAllPanels().subscribe();
        })
      );
  }

  /**
   * Enable tenant
   */
  enableTenant(tenantId: string): Observable<AdminPanelTenant> {
    this.loadingSubject.next(true);

    return this.http.put<AdminPanelTenant>(`${this.baseUrl}/panels/${tenantId}/enable`, {})
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
          // Refresh panels list
          this.getAllPanels().subscribe();
        })
      );
  }

  /**
   * Delete tenant
   */
  deleteTenant(tenantId: string): Observable<string> {
    this.loadingSubject.next(true);

    return this.http.delete<string>(`${this.baseUrl}/panels/${tenantId}`)
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
          // Refresh panels list
          this.getAllPanels().subscribe();
        })
      );
  }

  /**
   * Get domain information by tenant ID
   */
  getDomainInfo(tenantId: string): Observable<any> {
    return this.http.get<any>(`${this.baseUrl}/panels/${tenantId}/info`);
  }

  /**
   * Get all orders from all tenants - Fixed for proper pagination
   */
  getAllOrders(searchRequest: any): Observable<OrderPageResponse<any>> {
    let params = new HttpParams();

    if (searchRequest.search) {
      params = params.set('search', searchRequest.search);
    }
    if (searchRequest.tenantId) {
      params = params.set('tenantId', searchRequest.tenantId);
    }
    if (searchRequest.status) {
      params = params.set('status', searchRequest.status);
    }
    if (searchRequest.startDate) {
      params = params.set('startDate', searchRequest.startDate);
    }
    if (searchRequest.endDate) {
      params = params.set('endDate', searchRequest.endDate);
    }
    if (searchRequest.pageNumber !== undefined) {
      params = params.set('page', searchRequest.pageNumber.toString());
    }
    if (searchRequest.pageSize !== undefined) {
      params = params.set('size', searchRequest.pageSize.toString());
    }

    return this.http.get<OrderPageResponse<any>>(`${this.baseUrl}/orders`, { params });
  }

  /**
   * Reset password for a main tenant user
   */
  resetUserPassword(userId: number): Observable<any> {
    this.loadingSubject.next(true);

    return this.http.put<any>(`${this.configService.apiUrl}/users/${userId}/reset-password`, {})
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
        }),
        catchError((error: any) => {
          this.loadingSubject.next(false);
          throw error;
        })
      );
  }

  /**
   * Ban (deactivate) a main tenant user
   */
  banUser(userId: number): Observable<any> {
    this.loadingSubject.next(true);

    return this.http.put<any>(`${this.configService.apiUrl}/users/${userId}/deactivate`, {})
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
          // Refresh users list
          this.getMainTenantUsers(this.usersPaginationSubject.value.page_number, this.usersPaginationSubject.value.page_size).subscribe();
        }),
        catchError((error: any) => {
          this.loadingSubject.next(false);
          throw error;
        })
      );
  }

  /**
   * Unban (activate) a main tenant user
   */
  unbanUser(userId: number): Observable<any> {
    this.loadingSubject.next(true);

    return this.http.put<any>(`${this.configService.apiUrl}/users/${userId}/active`, {})
      .pipe(
        tap(() => {
          this.loadingSubject.next(false);
          // Refresh users list
          this.getMainTenantUsers(this.usersPaginationSubject.value.page_number, this.usersPaginationSubject.value.page_size).subscribe();
        }),
        catchError((error: any) => {
          this.loadingSubject.next(false);
          throw error;
        })
      );
  }

  // Getters for current values
  get panelsValue(): AdminPanelTenant[] {
    return this.panelsSubject.value;
  }

  get usersValue(): AdminPanelUser[] {
    return this.usersSubject.value;
  }

  get panelsPaginationValue(): any {
    return this.panelsPaginationSubject.value;
  }

  get usersPaginationValue(): any {
    return this.usersPaginationSubject.value;
  }
}