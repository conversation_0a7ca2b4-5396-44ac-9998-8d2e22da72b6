<ng-container *ngIf="state$ | async as state">
  <div class="w-full flex flex-col gap-4 md:p-6">



    <!-- Row with two columns for deposit and transaction history -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 w-full">
      <!-- First column - Deposit -->
      <div class="flex flex-col w-full gap-3 p-3 bg-white rounded-2xl h-auto">
        <!-- Main Content -->
        <div class="grid grid-cols-1 gap-4 mx-2 mb-4">
          <!-- Deposit Section -->
          <div>
            <h2 class="text-lg font-bold mb-3 text-gray-700">{{ 'add_fund.deposit' | translate }}</h2>
            <p class="text-sm text-gray-600 mb-3">{{ 'add_fund.payment_method' | translate }}</p>

            <!-- Payment Method Dropdown -->
            <div class="relative mb-3">
              <div class="w-full">
                <button
                  type="button"
                  class="w-full border border-gray-300 rounded-lg p-2 text-left bg-white flex items-center justify-between"
                  (click)="toggleDropdown()"
                  [class.border-primary]="state.showDropdown">
                  <div class="flex items-center gap-2">
                    <span *ngIf="state.selectedPaymentMethod" class="flex items-center gap-2">
                        <img *ngIf="state.selectedPaymentMethod.icon" [src]="getBankIconUrl(state.selectedPaymentMethod.icon)" alt="Payment Method Icon" class="w-6 h-6">

                      <span class="text-sm">{{ state.selectedPaymentMethod.name }}</span>
                      <span *ngIf="hasBonus(state.selectedPaymentMethod)" class="text-xs bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded-full">
                        (has bonus)
                      </span>
                    </span>
                    <span *ngIf="!state.selectedPaymentMethod" class="text-gray-500 text-sm">
                      {{ 'add_fund.select_payment_method' | translate }}
                    </span>
                  </div>
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>

                <!-- Dropdown Menu -->
                <div *ngIf="state.showDropdown" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg">
                  <!-- Search Input -->
                  <div class="p-2 border-b border-gray-200">
                    <input
                      type="text"
                      [ngModel]="state.searchTerm"
                      (input)="onSearchChange($any($event.target).value)"
                      placeholder="{{ 'add_fund.search_payment_methods' | translate }}"
                      class="w-full border border-gray-300 rounded-md px-2 py-1 text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                  </div>

                  <!-- Payment Method Options -->
                  <div class="max-h-48 overflow-y-auto">
                    <div *ngIf="state.isLoadingPaymentMethods" class="p-3 text-center text-gray-500 text-sm">
                      {{ 'common.loading' | translate }}...
                    </div>

                    <div *ngIf="!state.isLoadingPaymentMethods && state.filteredPaymentMethods.length === 0" class="p-3 text-center text-gray-500 text-sm">
                      {{ 'add_fund.no_payment_methods_found' | translate }}
                    </div>

                    <button
                      *ngFor="let method of state.filteredPaymentMethods"
                      type="button"
                      class="w-full text-left px-3 py-2 hover:bg-gray-50 flex items-center gap-2 border-b border-gray-100 last:border-b-0"
                      (click)="selectPaymentMethod(method)">
                      <img *ngIf="method.icon" [src]="getBankIconUrl(method.icon)" alt="Payment Method Icon" class="w-6 h-6">
                      <div class="flex-1">
                        <div class="font-medium text-sm">{{ method.name }}</div>
                        <div class="text-xs text-gray-500">{{ method.bank_name }}</div>
                      </div>
                      <span *ngIf="hasBonus(method)" class="text-xs bg-yellow-100 text-yellow-800 px-1 py-0.5 rounded-full">
                        (has bonus)
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Information Section -->
            <div *ngIf="state.selectedPaymentMethod"
              class="bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-200 rounded-xl p-4 mb-4 flex flex-col items-center shadow-lg">

              <!-- Manual Payment Method Display -->
              <div *ngIf="isManualPaymentMethod(state.selectedPaymentMethod); else standardPaymentMethod">
                <!-- QR Code for Manual Payment (if available) -->
                <div *ngIf="getManualPaymentInfo(state.selectedPaymentMethod)?.qrImageUrl"
                     class="bg-white p-4 rounded-xl shadow-md border-4 border-blue-100 mb-4">
                  <img [src]="getManualPaymentInfo(state.selectedPaymentMethod).qrImageUrl"
                       alt="QR Code" class="w-48 h-48 mx-auto">
                </div>
              </div>

              <!-- Standard Payment Method Display -->
              <ng-template #standardPaymentMethod>
                <!-- QR Code with enhanced styling -->
                <div class="bg-white p-4 rounded-xl shadow-md border-4 border-blue-100 mb-4">
                  <img [src]="getQRCodeUrl()" alt="QR Code" class="w-48 h-48 mx-auto"
                       onerror="this.src='assets/images/qr-code.png'">
                </div>
              </ng-template>

              <!-- Manual Payment Information -->
              <div *ngIf="isManualPaymentMethod(state.selectedPaymentMethod)"
                   class="bg-white rounded-xl p-3 w-full shadow-md border border-blue-100">
                <div class="text-center space-y-2">
                  <!-- Custom Key-Value Pairs -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-2">
                    <div class="grid grid-cols-1 gap-1 text-sm">
                      <div *ngFor="let pair of getManualPaymentInfo(state.selectedPaymentMethod)?.keyValuePairs"
                           class="flex justify-between items-center">
                        <span class="text-gray-600">{{ pair.key }}:</span>
                        <div class="flex items-center gap-1">
                          <span class="font-semibold text-blue-700">{{ pair.value }}</span>
                          <button (click)="copyToClipboard(pair.value)"
                                  class="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Transfer Content for Manual Payment -->
                  <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                    <div class="flex justify-between items-center">
                      <span class="text-sm text-gray-600">{{ 'add_fund.content' | translate }}:</span>
                      <div class="flex items-center gap-1">
                        <span class="font-bold text-yellow-700 font-mono">{{ getTransferContent() }}</span>
                        <button (click)="copyTransferContent()" class="bg-yellow-500 hover:bg-yellow-600 text-white p-1 rounded text-xs">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Standard Transfer Information - Compact Layout -->
              <div *ngIf="!isManualPaymentMethod(state.selectedPaymentMethod)"
                   class="bg-white rounded-xl p-3 w-full shadow-md border border-blue-100">
                <div class="text-center space-y-2">
                  <!-- Bank Information - Compact Grid -->
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-2">
                    <div class="grid grid-cols-1 gap-1 text-sm">
                      <div class="flex justify-between items-center">
                        <span class="text-gray-600">{{ 'add_fund.bank' | translate }}:</span>
                         <div class="flex items-center gap-1">
                        <span class="font-semibold text-blue-700">{{ state.selectedPaymentMethod.bank_name }}</span>
                         <button (click)="copyBankName()" class="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                          </div>
                      </div>
                      <div class="flex justify-between items-center">
                        <span class="text-gray-600">{{ 'add_fund.account' | translate }}:</span>
                        <div class="flex items-center gap-1">
                          <span class="font-bold text-blue-800 font-mono">{{ state.selectedPaymentMethod.account_number }}</span>
                          <button (click)="copyAccountNumber()" class="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button>
                        </div>
                      </div>
                      <div class="flex justify-between items-center">
                        <span class="text-gray-600">{{ 'add_fund.name' | translate }}:</span>
                          <div class="flex items-center gap-1">
                        <span class="font-semibold text-blue-700">{{ state.selectedPaymentMethod.account_name }}</span>
                         <button (click)="copyAccountName()" class="bg-blue-500 hover:bg-blue-600 text-white p-1 rounded text-xs">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                            </svg>
                          </button></div>
                      </div>
                    </div>
                  </div>

                  <!-- Transfer Content - Compact -->
                  <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-2">
                    <div class="flex justify-between items-center">
                      <span class="text-sm text-gray-600">{{ 'add_fund.content' | translate }}:</span>
                      <div class="flex items-center gap-1">
                        <span class="font-bold text-yellow-700 font-mono">{{ getTransferContent() }}</span>
                        <button (click)="copyTransferContent()" class="bg-yellow-500 hover:bg-yellow-600 text-white p-1 rounded text-xs">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Payment Method Description -->
            <div *ngIf="state.selectedPaymentMethod && state.selectedPaymentMethod.details" class="mb-6">
              <div class="bg-blue-50 border border-blue-100 p-4 rounded-md">
                <div [innerHTML]="state.selectedPaymentMethod.details"></div>
              </div>
            </div>

            <!-- Bonus Conditions Table -->
            <div *ngIf="state.selectedPaymentMethod && hasBonus(state.selectedPaymentMethod)" class="mb-4">
              <h4 class="text-lg font-semibold mb-2 text-gray-700">{{ 'add_fund.bonus_conditions' | translate }}</h4>
              <div class=" border border-gray-200 rounded-lg overflow-hidden">
                <table class="w-full">
                  <thead class=" border-b border-gray-200">
                    <tr>
                      <th class="px-3 py-2 text-left text-sm font-medium text-gray-700 border-r border-gray-200">{{ 'add_fund.deposit' | translate }}</th>
                      <th class="px-3 py-2 text-left text-sm font-medium text-gray-700">{{ 'add_fund.bonus' | translate }}</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200">
                    <tr *ngFor="let condition of state.selectedPaymentMethod.conditions" class="hover:bg-gray-50">
                     <td class="px-3 py-2 text-sm text-gray-700 border-r border-gray-200">
  {{ formatNumberWithCommas(condition.min_amount) }} 
</td>
                      <td class="px-3 py-2 text-sm text-gray-700 font-medium">{{ condition.bonus_amount }}%</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Amount Selection -->
            <div class="mb-3">
              <h3 class="text-base font-bold mb-2 text-gray-700">{{ 'add_fund.amount' | translate }}</h3>
              <div class="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
                <button *ngFor="let amount of state.amountOptions" [ngClass]="{
                  'border-blue-500 text-blue-500 hover:bg-blue-50': state.selectedAmount === amount,
                  'border-gray-300 hover:bg-gray-50': state.selectedAmount !== amount
                }" (click)="updateSelectedAmount(amount)" class="border py-1 rounded-md text-sm">
                  {{ amount }}
                </button>
              </div>
             <input type="text"
       [ngModel]="state.customAmount"
       (input)="onCustomAmountInput($event)"
       placeholder="{{ 'add_fund.custom_amount' | translate }}"
       [attr.maxlength]="state.selectedPaymentMethod ? state.selectedPaymentMethod.max_amount.toString().length + 3 : null"
       [class]="'w-full border p-2 rounded-md text-sm focus:outline-none focus:ring-1 ' +
               (state.amountValidation.isValid ? 'border-gray-300 focus:ring-blue-500 focus:border-blue-500' : 'border-red-300 focus:ring-red-500 focus:border-red-500')">

              <!-- Amount validation message -->
              <div *ngIf="!state.amountValidation.isValid && state.amountValidation.message"
                   class="mt-1 text-xs text-red-600 bg-red-50 border border-red-200 rounded-md p-2">
                <i class="fas fa-exclamation-triangle mr-1"></i>
                {{ state.amountValidation.message }}
              </div>

              <!-- Amount limits info -->
           <div *ngIf="state.selectedPaymentMethod" class="mt-1 text-xs text-gray-500">
  {{ 'add_fund.amount_limits' | translate }}:
  {{ formatNumberWithCommas(state.selectedPaymentMethod.min_amount) }} -
  {{ formatNumberWithCommas(state.selectedPaymentMethod.max_amount) }} {{ state.selectedPaymentMethod.bank_currency_code || 'VND' }}
</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Second column - Transaction History -->
      <div class="flex flex-col w-full p-3 bg-white rounded-2xl h-fit">
        <h2 class="text-lg font-bold mb-3 text-gray-700">{{ 'add_fund.transaction_history' | translate }}</h2>

        <!-- Date Filter -->
        <div class="mb-3">
          <label class="block text-sm font-medium text-gray-700 mb-1">{{ 'add_fund.date_range' | translate }}</label>
          <app-date-range-picker
            [ngModel]="state.dateRange"
            (dateRangeChanged)="onDateRangeChange($event)"
            containerClass="w-full">
          </app-date-range-picker>
        </div>

        <!-- Transaction Table -->
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead>
              <tr>
                <th *ngFor="let header of tableHeaders"
                  class="p-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  {{ header.label | translate }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr *ngIf="state.isLoadingTransactions">
                <td [attr.colspan]="tableHeaders.length" class="p-2 text-center text-gray-500 text-sm">
                  {{ 'common.loading' | translate }}...
                </td>
              </tr>
              <tr *ngIf="!state.isLoadingTransactions && state.transactions.length === 0">
                <td [attr.colspan]="tableHeaders.length" class="p-2 text-center text-gray-500 text-sm">
                  {{ 'add_fund.no_transactions' | translate }}
                </td>
              </tr>
              <tr *ngFor="let transaction of state.transactions">
                <td class="p-2 whitespace-nowrap text-sm">{{ transaction.id }}</td>
                <td class="p-2 whitespace-nowrap text-sm">{{ formatDate(transaction.created_at) }}</td>
                <td class="p-2 whitespace-nowrap text-sm">
                  <span class="px-1 py-0.5 text-xs rounded-full"
                        [ngClass]="{
                          'bg-green-100 text-green-800': transaction.type === 'Deposit',
                          'bg-yellow-100 text-yellow-800': transaction.type === 'Bonus'
                        }">
                    {{ transaction.type }}
                  </span>
                </td>
             <td class="p-2 whitespace-nowrap text-sm font-medium"
    [ngClass]="{
      'text-green-600': transaction.change > 0,
      'text-red-600': transaction.change < 0
    }">
  {{ formatNumberWithCommas(transaction.change) }} $
</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</ng-container>
