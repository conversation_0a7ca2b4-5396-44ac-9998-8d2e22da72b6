/* Order Tracking Component Styles */
.order-tracking-container {
  min-height: 100vh;
  background-color: #f9fafb;
  padding: 1.5rem;
}

/* Table styling */
table {
  border-collapse: separate;
  border-spacing: 0;
}

th {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Filter section */
.filter-section {
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* Status badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Loading animation */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Hover effects */
.hover\:bg-gray-50:hover {
  background-color: #f9fafb;
}

.hover\:bg-blue-700:hover {
  background-color: #1d4ed8;
}

.hover\:bg-gray-700:hover {
  background-color: #374151;
}

/* Responsive design */
@media (max-width: 768px) {
  .order-tracking-container {
    padding: 1rem;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
  
  .flex.space-x-2 {
    flex-direction: column;
    space-x: 0;
  }
  
  .flex.space-x-2 > * + * {
    margin-top: 0.5rem;
    margin-left: 0;
  }
}

/* Table responsive */
.overflow-x-auto {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

/* Focus styles */
input:focus,
select:focus {
  outline: none;
  ring: 2px;
  ring-color: #3b82f6;
  border-color: transparent;
}

/* Disabled styles */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Animation for table rows */
tbody tr {
  transition: background-color 0.2s ease-in-out;
}

/* Custom scrollbar */
.overflow-x-auto::-webkit-scrollbar {
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Pagination Styles */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 0;
}

.page-numbers {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.page-nav {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: #fff;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.page-nav:hover:not(:disabled) {
  background-color: #f3f4f6;
}

.page-nav:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-num {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  padding: 0 0.75rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  background-color: #fff;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-num:hover:not(.active) {
  background-color: #f3f4f6;
}

.page-num.active {
  background-color: #3b82f6;
  color: #fff;
  border-color: #3b82f6;
}

.page-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.show-entries {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.show-entries select {
  padding: 0.25rem 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background-color: #fff;
  font-size: 0.875rem;
}

/* Mobile responsive pagination */
@media (max-width: 640px) {
  .pagination {
    flex-direction: column;
    gap: 1rem;
  }

  .page-info {
    order: -1;
    text-align: center;
  }

  .show-entries {
    justify-content: center;
  }
}
