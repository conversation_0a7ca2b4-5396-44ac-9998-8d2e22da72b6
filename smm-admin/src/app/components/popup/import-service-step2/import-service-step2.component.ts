import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { SuperGeneralSvReq } from '../../../model/request/super-general-sv-req.model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { IconDropdownComponent } from '../../../components/common/icon-dropdown/icon-dropdown.component';
import { IconBaseModel } from '../../../model/base-model';
import { ToastService } from '../../../core/services/toast.service';
import { NotifyType } from '../../../constant/notify-type';
import { CurrencyService } from '../../../core/services/currency.service';

// Enum for service types
export enum AddType {
  Manual = 'Manual',
  Api = 'Api'
}

// Interface for import result
interface ImportResult {
  successServices: string[];
  failedServices: { name: string; error: string }[];
}

@Component({
  selector: 'app-import-service-step2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TranslateModule,
    IconDropdownComponent
  ],
  templateUrl: './import-service-step2.component.html',
  styleUrl: './import-service-step2.component.css'
})
export class ImportServiceStep2Component implements OnInit {
  @Input() selectedServices: SuperGeneralSvRes[] = [];
  @Input() selectedProvider: any = { id: 0 }; // Added Input for selectedProvider
  @Output() close = new EventEmitter<void>();
  @Output() servicesConfigured = new EventEmitter<SuperGeneralSvRes[]>();
  @Output() back = new EventEmitter<void>();

  categories: SuperCategoryRes[] = [];
  categoryOptions: IconBaseModel[] = [];
  selectedCategoryOption: IconBaseModel | undefined;
  selectedCategory: SuperCategoryRes | null = null;
  extraPricePercentage: number = 30;
  servicePercentages: Map<number, number> = new Map<number, number>();
  isLoading: boolean = false;

  // Properties for tracking import progress
  totalServices: number = 0;
  processedServices: number = 0;
  successCount: number = 0;
  failedCount: number = 0;
  currentServiceName: string = '';
  serviceTypeOption: string = 'Default';

  // Selected service type (default to 'Default')
  finalPrice: number = 0;

  // Import result tracking
  private importResult: ImportResult = {
    successServices: [],
    failedServices: []
  };

  // Show result popup
  showResultPopup: boolean = false;

  constructor(
    private adminService: AdminServiceService,
    private toastService: ToastService,
    private currencyService: CurrencyService
  ) {}

  ngOnInit(): void {
    this.loadCategories();
    this.initializeServicePercentages();
  }

  // Update all services with the global percentage
  updateAllPercentages(): void {
    this.selectedServices.forEach(service => {
      this.servicePercentages.set(service.id, this.extraPricePercentage);
    });
  }

  initializeServicePercentages(): void {
    // Initialize all services with the default percentage
    this.selectedServices.forEach(service => {
      const serviceId = service.id;
      this.servicePercentages.set(serviceId, this.extraPricePercentage);
    });
  }

  loadCategories(): void {
    this.isLoading = true;
    this.adminService.categories$.subscribe({
      next: (categories) => {
        this.categories = categories;
        console.log('Categories:', categories);
        // Create category options for the dropdown
        this.categoryOptions = categories.map(category => ({
          id: category.id.toString(),
          label: category.name,
          sort: category.sort,
          icon: category.platformIcon || '' // Default icon if none provided
        }));

        if (categories.length > 0) {
          this.selectedCategory = categories[0];
          this.selectedCategoryOption = this.categoryOptions[0];
        }
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading categories:', error);
      },

    });
  }

  onCategoryChange(option: IconBaseModel): void {
    this.selectedCategoryOption = option;
    // Find the category object that matches the selected ID
    const category = this.categories.find(c => c.id.toString() === option.id);
    if (category) {
      this.selectedCategory = category;
    }
  }

  calculateFinalPrice(service: SuperGeneralSvRes): number {
    const serviceId = service.id;
    const percentage = this.servicePercentages.get(serviceId) || this.extraPricePercentage;
    const multiplier = 1 + (percentage / 100);
    const calculatedPrice = service.price * multiplier;
    return parseFloat(this.currencyService.formatBalance(calculatedPrice));
  }

  getServicePercentage(serviceId: number): number {
    return this.servicePercentages.get(serviceId) || this.extraPricePercentage;
  }

  updateServicePercentage(serviceId: number, percentage: number): void {
    this.servicePercentages.set(serviceId, percentage);
  }

  handlePercentageInput(serviceId: number, event: Event): void {
    const input = event.target as HTMLInputElement;
    const value = parseFloat(input.value);
    if (!isNaN(value)) {
      this.updateServicePercentage(serviceId, value);
    }
  }

  getServiceId(service: any): string {
    // Handle both SuperGeneralSvRes (with id) and provider services (with service property)
    return service.service || service.id || service.api_service_id || '';
  }

  /**
   * Get the provider name for display
   */
  getProviderName(): string {
    return this.selectedProvider?.name || 'Unknown Provider';
  }

  configureServices(): void {
    if (!this.selectedCategory) {
      return;
    }

    this.isLoading = true;
    this.totalServices = this.selectedServices.length;
    this.processedServices = 0;
    this.successCount = 0;
    this.failedCount = 0;
    
    // Reset import result
    this.importResult = {
      successServices: [],
      failedServices: []
    };

    // Start processing services sequentially
    this.processNextService();
  }

  /**
   * Process services one by one with 3 second delay
   */
  private async processNextService(): Promise<void> {
    // Check if we've processed all services
    if (this.processedServices >= this.totalServices) {
      this.completeImport();
      return;
    }

    // Get the next service to process
    const service = this.selectedServices[this.processedServices];
    this.currentServiceName = service.name;

    // Calculate the final price with markup
    const serviceId = service.id;
    const percentage = this.servicePercentages.get(serviceId) || this.extraPricePercentage;
    const multiplier = 1 + (percentage / 100);
    const calculatedPrice = service.price * multiplier;
    this.finalPrice = parseFloat(this.currencyService.formatBalance(calculatedPrice));

    // Create the request object
    const newService: SuperGeneralSvReq = this.createServiceRequest(service, percentage);

    // Call the API to create the service
    this.adminService.createService(newService).subscribe({
      next: (createdService) => {
        console.log('Service created successfully:', createdService);
        this.successCount++;
        this.processedServices++;
        
        // Add to success list
        this.importResult.successServices.push(service.name);

        // Wait 3 seconds before processing next service
        this.waitAndProcessNext();
      },
      error: (error) => {
        console.error('Error creating service:', error);
        this.failedCount++;
        this.processedServices++;
        
        // Add to failed list
        this.importResult.failedServices.push({
          name: service.name,
          error: error.message || 'Unknown error'
        });

        // Wait 3 seconds before processing next service
        this.waitAndProcessNext();
      }
    });
  }

  /**
   * Wait 3 seconds and process next service
   */
  private waitAndProcessNext(): void {
    setTimeout(() => {
      this.processNextService();
    }, 3000); // 3 seconds delay
  }

  /**
   * Create a service request object from a service response
   */
  private createServiceRequest(service: SuperGeneralSvRes, percentage: number): SuperGeneralSvReq {
    // Extract required values
    const serviceName = service.name;
    const description = service.description || '';
    const min = service.min;
    const max = service.max;
    const serviceId = this.getServiceId(service);
    const providerPrice = service.price;
    const extraPricePercentage = percentage;

    // Create the request object
    return {
      name: serviceName,
      speed_per_day: null,
      description: description,
      price: this.finalPrice,
      min: min,
      max: max,
      type: this.serviceTypeOption, // Already a string value from the dropdown
      api_service_id: typeof serviceId === 'string' ? parseInt(serviceId) || null : serviceId,
      // Add other required fields
      price1: 0,
      price2: 0,
      original_price: providerPrice,

      auto_sync: false,

      labels: [], // Empty array instead of null
      percent: extraPricePercentage,
      percent1: 0,
      percent2: 0,
      add_type: AddType.Api, // Always set to Api
      category_id: parseInt(this.selectedCategory?.id?.toString() || '0'), // Set based on selected category
      api_provider_id: this.selectedProvider.id,
      average_time: 0,

      refill: false,
      // Add the additional fields
      is_overflow: false,
      overflow: 0,
      refill_days: '', // Empty string instead of null
      sample_link: '',
      is_fixed_price: false,
      cancel_button: false
    };
  }

  /**
   * Complete the import process and show result popup
   */
  private completeImport(): void {
    this.isLoading = false;
    this.showResultPopup = true;
    // Don't close parent popup here, let result popup handle it
  }

  /**
   * Close result popup and emit configured services
   */
  closeResultPopup(): void {
    this.showResultPopup = false;

    // Close the parent popup
    this.close.emit();

    // Create a list of configured services to emit
    const configuredServices = this.selectedServices.map(service => ({
      ...service,
      category_id: this.selectedCategory?.id || 0
    }));

    // Emit the configured services
    this.servicesConfigured.emit(configuredServices);
  }

  /**
   * Get import result for display
   */
  getImportResult(): ImportResult {
    return this.importResult;
  }

  goBack(): void {
    this.back.emit();
  }

  closePopup(): void {
    this.close.emit();
  }

  onOverlayClick(event: MouseEvent): void {
    // Close if clicking on the overlay background, not the modal content
    if (event.target === event.currentTarget) {
      this.closePopup();
    }
  }

  onResultOverlayClick(event: MouseEvent): void {
    // Close result popup if clicking on the overlay background
    if (event.target === event.currentTarget) {
      this.closeResultPopup();
    }
  }
}