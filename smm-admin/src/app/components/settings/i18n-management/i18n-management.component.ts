import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { IconsModule } from '../../../icons/icons.module';
import { Subscription } from 'rxjs';
import { Location } from '@angular/common';

import { TenantI18nService, TenantI18nContentRes, TenantI18nContentReq } from '../../../core/services/tenant-i18n.service';
import { ToastService } from '../../../core/services/toast.service';
import { TenantSettingsService } from '../../../core/services/tenant-settings.service';
import { CustomLanguageReq } from '../../../model/request/custom-language-req.model';
import { CustomLanguageRes } from '../../../model/response/custom-language-res.model';
import { LanguageOptionRes } from '../../../model/response/language-option-res.model';


interface TranslationItem {
  key: string;
  value: string;
  originalValue?: string;
  isModified?: boolean;
}

@Component({
  selector: 'app-i18n-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule,
    IconsModule,

  ],
  templateUrl: './i18n-management.component.html',
  styleUrl: './i18n-management.component.css'
})
export class I18nManagementComponent implements OnInit, OnDestroy {

  // Tab management
  activeTab: 'translations' | 'languages' = 'languages';

  // Language management
  predefinedLanguages: LanguageOptionRes[] = [];
  filteredPredefinedLanguages: LanguageOptionRes[] = [];
  predefinedLanguagesSearchTerm = '';
  customLanguages: CustomLanguageRes[] = [];
  allAvailableLanguages: LanguageOptionRes[] = [];
  selectedLanguage = 'vi';
  customizedLanguages: string[] = []; // Languages available for translation editing (from tenant settings)

  // Translation management
  translations: TranslationItem[] = [];
  filteredTranslations: TranslationItem[] = [];
  searchTerm = '';
  selectedCategory = '';
  categories: string[] = [];

  // UI state
  isLoading = false;
  isSaving = false;
  showLanguageDropdown = false;

  // Custom language form
  showCustomLanguageForm = false;
  editingCustomLanguage: CustomLanguageRes | null = null;
  customLanguageForm: CustomLanguageReq = {
    language_code: '',
    language_name: '',
    flag_class: '',
    description: '',
    active: true
  };

  // Language selection for tenant
  selectedAvailableLanguages: string[] = [];
  defaultLanguage = 'vi';

  // Statistics
  totalKeys = 0;
  customizedKeys = 0;
  lastModified?: string;

  private subscriptions: Subscription[] = [];

  constructor(
    private tenantI18nService: TenantI18nService,
    private toastService: ToastService,
    private tenantSettingsService: TenantSettingsService,
    private location: Location
  ) {}

  ngOnInit(): void {
    this.loadAllLanguageData();
    this.loadTranslations();
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Load all language data (predefined + custom)
  loadAllLanguageData(): void {
    this.isLoading = true;

    // Load predefined languages
    const predefinedSub = this.tenantSettingsService.getPredefinedLanguages().subscribe({
      next: (languages) => {
        this.predefinedLanguages = languages;
        this.filteredPredefinedLanguages = languages;
        this.updateAllAvailableLanguages();
      },
      error: (error) => {
        console.error('Error loading predefined languages:', error);
        this.toastService.showError('Failed to load predefined languages');
      }
    });
    this.subscriptions.push(predefinedSub);

    // Load custom languages
    const customSub = this.tenantSettingsService.getCustomLanguages().subscribe({
      next: (languages) => {
        this.customLanguages = languages;
        this.updateAllAvailableLanguages();
      },
      error: (error) => {
        console.error('Error loading custom languages:', error);
        this.toastService.showError('Failed to load custom languages');
      }
    });
    this.subscriptions.push(customSub);

    // Load tenant language settings
    const settingsSub = this.tenantSettingsService.getLanguageSettings().subscribe({
      next: (settings) => {
        this.defaultLanguage = settings.default_language;
        this.selectedAvailableLanguages = settings.available_languages;

        // Update available languages for translation editing after loading settings
        this.loadAvailableLanguages();

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading language settings:', error);
        this.isLoading = false;
      }
    });
    this.subscriptions.push(settingsSub);
  }

  updateAllAvailableLanguages(): void {
    this.allAvailableLanguages = [
      ...this.predefinedLanguages,
      ...this.customLanguages.map(custom => ({
        code: custom.language_code,
        name: custom.language_name,
        flag: custom.flag_class || 'fi fi-xx',
        type: 'custom' as const,
        active: custom.active,
        description: custom.description
      }))
    ];
  }

  loadAvailableLanguages(): void {
    // Use selectedAvailableLanguages from tenant settings for translation editing
    // This ensures the dropdown shows all languages configured for the tenant
    if (this.selectedAvailableLanguages && this.selectedAvailableLanguages.length > 0) {
      this.customizedLanguages = [...this.selectedAvailableLanguages];
      if (!this.customizedLanguages.includes(this.selectedLanguage)) {
        this.selectedLanguage = this.customizedLanguages[0];
      }
    } else {
      // Fallback: load from API if selectedAvailableLanguages is not yet loaded
      const availableSub = this.tenantSettingsService.getTenantAvailableLanguages().subscribe({
        next: (languages) => {
          this.customizedLanguages = languages;
          if (languages.length > 0 && !languages.includes(this.selectedLanguage)) {
            this.selectedLanguage = languages[0];
          }
        },
        error: (error) => {
          console.error('Error loading available languages:', error);
          // Fallback to default languages if API fails
          this.customizedLanguages = ['vi', 'en', 'zh'];
          if (!this.customizedLanguages.includes(this.selectedLanguage)) {
            this.selectedLanguage = this.customizedLanguages[0];
          }
        }
      });
      this.subscriptions.push(availableSub);
    }
  }

  loadTranslations(): void {
    if (!this.selectedLanguage) return;

    this.isLoading = true;
    const sub = this.tenantI18nService.getI18nContent(this.selectedLanguage).subscribe({
      next: (response) => {
        this.processTranslationsResponse(response);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading translations:', error);
        this.loadDefaultTemplate();
      }
    });
    this.subscriptions.push(sub);
  }

  loadDefaultTemplate(): void {
    if (!this.selectedLanguage) {
      this.toastService.showWarning('Please select a language first');
      return;
    }

    this.isLoading = true;
    const sub = this.tenantI18nService.getTemplateForLanguage(this.selectedLanguage).subscribe({
      next: (template) => {
        this.processDefaultTemplate(template);
        this.isLoading = false;
        this.toastService.showSuccess(`Template loaded for ${this.selectedLanguage}`);
      },
      error: (error) => {
        console.error('Error loading template:', error);
        // Call getDefaultTemplate when there's an error
        this.getDefaultTemplate();
      }
    });
    this.subscriptions.push(sub);
  }

  getDefaultTemplate(): void {
    const sub = this.tenantI18nService.getDefaultTemplate().subscribe({
      next: (template) => {
        this.processDefaultTemplate(template);
        this.isLoading = false;
        this.toastService.showSuccess('Default template loaded');
      },
      error: (error) => {
        console.error('Error loading default template:', error);
        this.toastService.showError('Failed to load default translation template');
        this.isLoading = false;
      }
    });
    this.subscriptions.push(sub);
  }

  processTranslationsResponse(response: TenantI18nContentRes): void {
    this.totalKeys = response.total_keys;
    this.customizedKeys = response.customized_keys;
    this.lastModified = response.last_modified;

    // Convert flat translations to TranslationItem array
    this.translations = Object.entries(response.translations).map(([key, value]) => ({
      key,
      value,
      originalValue: value,
      isModified: false
    }));

    this.extractCategories();
    this.applyFilters();
  }

  processDefaultTemplate(template: { [key: string]: any }): void {
    // Flatten nested template object
    const flatTemplate = this.flattenObject(template);

    this.translations = Object.entries(flatTemplate).map(([key, value]) => ({
      key,
      value: String(value),
      originalValue: String(value),
      isModified: false
    }));

    this.totalKeys = this.translations.length;
    this.customizedKeys = 0;

    this.extractCategories();
    this.applyFilters();
  }

  flattenObject(obj: any, prefix = ''): { [key: string]: string } {
    const result: { [key: string]: string } = {};

    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = prefix ? `${prefix}.${key}` : key;

        if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
          Object.assign(result, this.flattenObject(obj[key], newKey));
        } else {
          result[newKey] = String(obj[key]);
        }
      }
    }

    return result;
  }

  extractCategories(): void {
    const categorySet = new Set<string>();
    this.translations.forEach(item => {
      const parts = item.key.split('.');
      if (parts.length > 1) {
        categorySet.add(parts[0]);
      }
    });
    this.categories = Array.from(categorySet).sort();
  }

  onLanguageChange(): void {
    this.loadTranslations();
  }

  onTranslationChange(item: TranslationItem): void {
    item.isModified = item.value !== item.originalValue;
  }

  applyFilters(): void {
    this.filteredTranslations = this.translations.filter(item => {
      const matchesSearch = !this.searchTerm ||
        item.key.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
        item.value.toLowerCase().includes(this.searchTerm.toLowerCase());

      const matchesCategory = !this.selectedCategory ||
        item.key.startsWith(this.selectedCategory + '.');

      return matchesSearch && matchesCategory;
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onCategoryChange(): void {
    this.applyFilters();
  }

  onPredefinedLanguagesSearchChange(): void {
    this.filterPredefinedLanguages();
  }

  filterPredefinedLanguages(): void {
    if (!this.predefinedLanguagesSearchTerm.trim()) {
      this.filteredPredefinedLanguages = [...this.predefinedLanguages];
    } else {
      const searchTerm = this.predefinedLanguagesSearchTerm.toLowerCase();
      this.filteredPredefinedLanguages = this.predefinedLanguages.filter(lang =>
        lang.name.toLowerCase().includes(searchTerm) ||
        lang.code.toLowerCase().includes(searchTerm)
      );
    }
  }

  saveTranslations(): void {
    if (this.translations.length === 0) {
      this.toastService.showWarning('No translations to save');
      return;
    }

    this.isSaving = true;

    // Convert back to nested object structure
    const translationsObject = this.unflattenObject(
      this.translations.reduce((acc, item) => {
        acc[item.key] = item.value;
        return acc;
      }, {} as { [key: string]: string })
    );

    const request: TenantI18nContentReq = {
      language_code: this.selectedLanguage,
      translations: translationsObject
    };

    const sub = this.tenantI18nService.updateI18nContent(this.selectedLanguage, request).subscribe({
      next: (response) => {
        this.toastService.showSuccess('Translations saved successfully');
        this.processTranslationsResponse(response);
        this.loadAvailableLanguages();
        this.isSaving = false;
      },
      error: (error) => {
        console.error('Error saving translations:', error);
        this.toastService.showError('Failed to save translations');
        this.isSaving = false;
      }
    });
    this.subscriptions.push(sub);
  }

  unflattenObject(flatObj: { [key: string]: string }): { [key: string]: any } {
    const result: { [key: string]: any } = {};

    for (const key in flatObj) {
      const keys = key.split('.');
      let current = result;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        }
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = flatObj[key];
    }

    return result;
  }

  resetTranslations(): void {
    this.translations.forEach(item => {
      item.value = item.originalValue || '';
      item.isModified = false;
    });
    this.applyFilters();
  }

  getLanguageName(code: string): string {
    const lang = this.allAvailableLanguages.find((l: LanguageOptionRes) => l.code === code);
    return lang ? lang.name : code.toUpperCase();
  }

  getLanguageFlag(code: string): string {
    const lang = this.allAvailableLanguages.find((l: LanguageOptionRes) => l.code === code);
    return lang ? lang.flag : 'fi fi-xx';
  }

  hasUnsavedChanges(): boolean {
    return this.translations.some(item => item.isModified);
  }

  canSaveTranslations(): boolean {
    return this.translations.length > 0;
  }

  getModifiedCount(): number {
    return this.translations.filter(item => item.isModified).length;
  }

  trackByKey(_index: number, item: TranslationItem): string {
    return item.key;
  }

  // Download translations as JSON file
  downloadTranslations(): void {
    if (!this.selectedLanguage || this.translations.length === 0) {
      this.toastService.showWarning('No translations to download');
      return;
    }

    try {
      // Convert translations back to nested object structure
      const translationsObject = this.unflattenObject(
        this.translations.reduce((acc, item) => {
          acc[item.key] = item.value;
          return acc;
        }, {} as { [key: string]: string })
      );

      // Create downloadable content
      const content = JSON.stringify(translationsObject, null, 2);
      const blob = new Blob([content], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);

      // Create download link
      const link = document.createElement('a');
      link.href = url;
      link.download = `${this.selectedLanguage}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      this.toastService.showSuccess('Translations downloaded successfully');
    } catch (error) {
      console.error('Error downloading translations:', error);
      this.toastService.showError('Failed to download translations');
    }
  }

  // Handle file selection for upload
  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (!file) return;

    if (!file.name.endsWith('.json')) {
      this.toastService.showError('Please select a JSON file');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const jsonData = JSON.parse(content);
        this.uploadTranslations(jsonData);
      } catch (error) {
        console.error('Error parsing JSON file:', error);
        this.toastService.showError('Invalid JSON file format');
      }
    };
    reader.readAsText(file);

    // Reset file input
    event.target.value = '';
  }

  // Upload and process translations from JSON
  uploadTranslations(jsonData: any): void {
    try {
      if (!this.selectedLanguage) {
        this.toastService.showError('Please select a language first');
        return;
      }

      // Flatten the uploaded JSON data
      const flattenedData = this.flattenObject(jsonData);

      // Update existing translations or add new ones
      Object.keys(flattenedData).forEach(key => {
        const existingItem = this.translations.find(item => item.key === key);
        if (existingItem) {
          existingItem.value = flattenedData[key];
          existingItem.isModified = existingItem.value !== existingItem.originalValue;
        } else {
          // Add new translation key
          this.translations.push({
            key: key,
            value: flattenedData[key],
            originalValue: '',
            isModified: true
          });
        }
      });

      // Update filtered translations and categories
      this.extractCategories();
      this.applyFilters();

      this.toastService.showSuccess(`Uploaded ${Object.keys(flattenedData).length} translation keys`);
    } catch (error) {
      console.error('Error uploading translations:', error);
      this.toastService.showError('Failed to upload translations');
    }
  }

  // Tab management
  setActiveTab(tab: 'translations' | 'languages'): void {
    this.activeTab = tab;
  }

  // Language dropdown methods
  toggleLanguageDropdown(): void {
    this.showLanguageDropdown = !this.showLanguageDropdown;
  }

  selectLanguage(languageCode: string): void {
    if (languageCode !== this.selectedLanguage) {
      this.selectedLanguage = languageCode;
      this.showLanguageDropdown = false;
      this.onLanguageChange();
    } else {
      this.showLanguageDropdown = false;
    }
  }

  // Custom language management
  openCustomLanguageForm(): void {
    this.showCustomLanguageForm = true;
    this.editingCustomLanguage = null;
    this.resetCustomLanguageForm();
  }

  editCustomLanguage(language: CustomLanguageRes): void {
    this.showCustomLanguageForm = true;
    this.editingCustomLanguage = language;
    this.customLanguageForm = {
      language_code: language.language_code,
      language_name: language.language_name,
      flag_class: language.flag_class || '',
      description: language.description || '',
      active: language.active
    };
  }

  closeCustomLanguageForm(): void {
    this.showCustomLanguageForm = false;
    this.editingCustomLanguage = null;
    this.resetCustomLanguageForm();
  }

  resetCustomLanguageForm(): void {
    this.customLanguageForm = {
      language_code: '',
      language_name: '',
      flag_class: '',
      description: '',
      active: true
    };
  }

  saveCustomLanguage(): void {
    if (!this.customLanguageForm.language_code || !this.customLanguageForm.language_name) {
      this.toastService.showError('Language code and name are required');
      return;
    }

    this.isSaving = true;

    if (this.editingCustomLanguage) {
      // Update existing custom language
      const sub = this.tenantSettingsService.updateCustomLanguage(
        this.editingCustomLanguage.language_code,
        this.customLanguageForm
      ).subscribe({
        next: () => {
          this.toastService.showSuccess('Custom language updated successfully');
          this.loadAllLanguageData();
          this.closeCustomLanguageForm();
          this.isSaving = false;
        },
        error: (error) => {
          console.error('Error updating custom language:', error);
          this.toastService.showError('Failed to update custom language');
          this.isSaving = false;
        }
      });
      this.subscriptions.push(sub);
    } else {
      // Create new custom language
      const sub = this.tenantSettingsService.createCustomLanguage(this.customLanguageForm).subscribe({
        next: () => {
          this.toastService.showSuccess('Custom language created successfully');
          this.loadAllLanguageData();
          this.closeCustomLanguageForm();
          this.isSaving = false;
        },
        error: (error) => {
          console.error('Error creating custom language:', error);
          this.toastService.showError('Failed to create custom language');
          this.isSaving = false;
        }
      });
      this.subscriptions.push(sub);
    }
  }

  deleteCustomLanguage(language: CustomLanguageRes): void {
    if (!confirm(`Are you sure you want to delete the custom language "${language.language_name}"?`)) {
      return;
    }

    const sub = this.tenantSettingsService.deleteCustomLanguage(language.language_code).subscribe({
      next: () => {
        this.toastService.showSuccess('Custom language deleted successfully');
        this.loadAllLanguageData();
      },
      error: (error) => {
        console.error('Error deleting custom language:', error);
        this.toastService.showError('Failed to delete custom language');
      }
    });
    this.subscriptions.push(sub);
  }


  // Language selection for tenant
  toggleLanguageSelection(languageCode: string): void {
    const index = this.selectedAvailableLanguages.indexOf(languageCode);
    if (index > -1) {
      // Remove language if it's already selected (but keep at least one)
      if (this.selectedAvailableLanguages.length > 1) {
        this.selectedAvailableLanguages.splice(index, 1);
        // If we removed the default language, set a new default
        if (this.defaultLanguage === languageCode) {
          this.defaultLanguage = this.selectedAvailableLanguages[0];
        }
      }
    } else {
      // Add language to selection
      this.selectedAvailableLanguages.push(languageCode);
    }

    // Update the translation editing dropdown immediately
    this.loadAvailableLanguages();
  }

  setDefaultLanguage(languageCode: string): void {
    if (this.selectedAvailableLanguages.includes(languageCode)) {
      this.defaultLanguage = languageCode;
    }
  }

  saveLanguageSettings(): void {
    if (this.selectedAvailableLanguages.length === 0) {
      this.toastService.showError('At least one language must be selected');
      return;
    }

    if (!this.selectedAvailableLanguages.includes(this.defaultLanguage)) {
      this.toastService.showError('Default language must be in the selected languages list');
      return;
    }

    this.isSaving = true;

    const request = {
      default_language: this.defaultLanguage,
      available_languages: this.selectedAvailableLanguages
    };

    const sub = this.tenantSettingsService.updateLanguageSettings(request).subscribe({
      next: () => {
        this.toastService.showSuccess('Language settings saved successfully');

        // Update the dropdown for translation editing
        this.loadAvailableLanguages();

        this.isSaving = false;
      },
      error: (error) => {
        console.error('Error saving language settings:', error);
        this.toastService.showError('Failed to save language settings');
        this.isSaving = false;
      }
    });
    this.subscriptions.push(sub);
  }

  // Close dropdown when clicking outside
  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.language-dropdown-button, .language-dropdown-menu');

    if (!dropdown && this.showLanguageDropdown) {
      this.showLanguageDropdown = false;
    }
  }

  // Navigation
  goBack(): void {
    this.location.back();
  }
}
