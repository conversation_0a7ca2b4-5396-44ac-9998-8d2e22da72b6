<div class="page-container">
  <div class="content-container">
    <!-- Page Header -->
    <div class="page-header">
      <div class="flex items-center gap-3">
        <button class="back-button" (click)="goBack()">
          <fa-icon [icon]="['fas', 'arrow-left']"></fa-icon>
        </button>
        <h1 class="page-title">{{ 'i18n_management.title' | translate }}</h1>
      </div>
    </div>

    <!-- Header -->
    <div class="header-section">
      <div class="flex justify-between mb-6">


        <div class="flex gap-3" *ngIf="activeTab === 'translations'">
          <!-- Statistics -->
          <div class="stats-card">
            <div class="text-sm text-gray-500">{{ 'Total Keys' | translate }}</div>
            <div class="text-lg font-semibold">{{ totalKeys }}</div>
          </div>
          <div class="stats-card">
            <div class="text-sm text-gray-500">{{ 'Customized' | translate }}</div>
            <div class="text-lg font-semibold text-blue-600">{{ customizedKeys }}</div>
          </div>
          <div class="stats-card" *ngIf="hasUnsavedChanges()">
            <div class="text-sm text-orange-500">{{ 'Unsaved Changes' | translate }}</div>
            <div class="text-lg font-semibold text-orange-600">{{ getModifiedCount() }}</div>
          </div>
        </div>
      </div>

      <!-- Tab Navigation -->
      <div class="tab-navigation">
        <nav class="flex space-x-8 border-b border-gray-200">
          <button type="button" class="tab-button" [class.active]="activeTab === 'languages'"
            (click)="setActiveTab('languages')">
            <fa-icon [icon]="['fas', 'globe']"></fa-icon>
            {{ 'i18n_management.languages_tab' | translate }}
          </button>
          <button type="button" class="tab-button" [class.active]="activeTab === 'translations'"
            (click)="setActiveTab('translations')">
            <fa-icon [icon]="['fas', 'language']"></fa-icon>
            {{ 'i18n_management.translations_tab' | translate }}
          </button>

        </nav>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">

      <!-- Translations Tab -->
      <div *ngIf="activeTab === 'translations'" class="translations-tab">
        <!-- Language Selection for Translations -->
        <div class="language-selection mb-6">
          <div class="flex items-center space-x-4">
            <div class="flex-1">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ 'i18n_management.select_language' | translate }}
              </label>
              <div class="relative">
                <button type="button" class="language-dropdown-button" (click)="toggleLanguageDropdown()"
                  [disabled]="isLoading">
                  <div class="flex items-center space-x-2">
                    <span [class]="getLanguageFlag(selectedLanguage)" class="flag-icon"></span>
                    <span>{{ getLanguageName(selectedLanguage) }}</span>
                  </div>
                  <fa-icon [icon]="['fas', 'chevron-down']" [class.rotate-180]="showLanguageDropdown"
                    class="transition-transform duration-200"></fa-icon>
                </button>

                <div *ngIf="showLanguageDropdown" class="language-dropdown-menu">
                  <div *ngFor="let lang of customizedLanguages" class="language-dropdown-item"
                    (click)="selectLanguage(lang)" [class.selected]="selectedLanguage === lang">
                    <span [class]="getLanguageFlag(lang)" class="flag-icon"></span>
                    <span>{{ getLanguageName(lang) }}</span>
                    <fa-icon *ngIf="selectedLanguage === lang" [icon]="['fas', 'check']"
                      class="text-blue-500"></fa-icon>
                  </div>

                  <div *ngIf="customizedLanguages.length === 0" class="language-dropdown-empty">
                    {{ 'No languages available for editing' | translate }}
                    <div class="text-xs text-gray-400 mt-1">
                      {{ 'Configure languages in Language Management & Settings tab first' | translate }}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="flex items-end space-x-2">
              <button type="button" class="btn btn-outline" (click)="loadDefaultTemplate()" [disabled]="isLoading">
                <fa-icon [icon]="['fas', 'file-download']"></fa-icon>
                {{ 'i18n_management.load_translations' | translate }}
              </button>
              <button type="button" class="btn btn-outline" (click)="downloadTranslations()"
                [disabled]="isLoading || !selectedLanguage">
                <fa-icon [icon]="['fas', 'download']"></fa-icon>
                {{ 'i18n_management.download_translations' | translate }}
              </button>

              <div class="relative">
                <input #fileInput type="file" accept=".json" (change)="onFileSelected($event)" class="hidden">
                <button type="button" class="btn btn-outline" (click)="fileInput.click()"
                  [disabled]="isLoading || !selectedLanguage">
                  <fa-icon [icon]="['fas', 'upload']"></fa-icon>
                  {{ 'i18n_management.upload_translations' | translate }}
                </button>
              </div>



              <button type="button" class="btn btn-primary" (click)="saveTranslations()"
                [disabled]="isSaving || !canSaveTranslations()">
                <fa-icon [icon]="['fas', 'save']" *ngIf="!isSaving"></fa-icon>
                <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isSaving"></fa-icon>
                {{ isSaving ? ('i18n_management.saving' | translate) : ('i18n_management.save_translations' | translate)
                }}
              </button>
            </div>
          </div>
        </div>

        <!-- Filters -->
        <div class="filters-section">
          <!-- Desktop/Tablet Layout -->
          <div class="hidden md:flex items-center space-x-4 mb-4">
            <!-- Search -->
            <div class="flex-1">
              <div class="relative">
                <fa-icon [icon]="['fas', 'search']"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></fa-icon>
                <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()"
                  placeholder="{{ 'Search translations...' | translate }}" class="form-input pl-10">
              </div>
            </div>

            <!-- Category Filter -->
            <div class="w-48">
              <select [(ngModel)]="selectedCategory" (ngModelChange)="onCategoryChange()" class="form-select">
                <option value="">{{ 'All Categories' | translate }}</option>
                <option *ngFor="let category of categories" [value]="category">
                  {{ category }}
                </option>
              </select>
            </div>

            <!-- Reset Button -->
            <button type="button" class="btn btn-outline" (click)="resetTranslations()"
              [disabled]="!hasUnsavedChanges()">
              <fa-icon [icon]="['fas', 'undo']"></fa-icon>
              {{ 'Reset' | translate }}
            </button>
          </div>

          <!-- Mobile Layout -->
          <div class="block md:hidden mb-4 space-y-3">
            <!-- Search Field -->
            <div class="w-full">
              <div class="relative">
                <fa-icon [icon]="['fas', 'search']"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></fa-icon>
                <input type="text" [(ngModel)]="searchTerm" (ngModelChange)="onSearchChange()"
                  placeholder="{{ 'Search translations...' | translate }}" class="form-input pl-10 w-full text-base">
              </div>
            </div>

            <!-- Filter Row -->
            <div class="flex items-center space-x-3">
              <!-- Category Filter -->
              <div class="flex-1">
                <select [(ngModel)]="selectedCategory" (ngModelChange)="onCategoryChange()"
                  class="form-select w-full text-base">
                  <option value="">{{ 'All Categories' | translate }}</option>
                  <option *ngFor="let category of categories" [value]="category">
                    {{ category }}
                  </option>
                </select>
              </div>

              <!-- Reset Button -->
              <button type="button" class="btn btn-outline px-3 py-2 flex-shrink-0" (click)="resetTranslations()"
                [disabled]="!hasUnsavedChanges()">
                <fa-icon [icon]="['fas', 'undo']"></fa-icon>
                <span class="hidden xs:inline ml-2">{{ 'Reset' | translate }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div *ngIf="isLoading" class="loading-state">
          <div class="flex items-center justify-center py-12">
            <fa-icon [icon]="['fas', 'spinner']" [spin]="true" class="text-2xl text-blue-500 mr-3"></fa-icon>
            <span class="text-gray-600">{{ 'Loading translations...' | translate }}</span>
          </div>
        </div>

        <!-- Translations Table -->
        <!-- Translations Table - Mobile Responsive -->
        <div *ngIf="!isLoading" class="translations-table">
          <div class="table-container">

            <!-- Desktop/Tablet View -->
            <div class="hidden md:block">
              <table class="w-full">
                <thead>
                  <tr class="border-b border-gray-200">
                    <th class="text-left py-3 px-4 font-medium text-gray-700 w-1/3">
                      {{ 'Translation Key' | translate }}
                    </th>
                    <th class="text-left py-3 px-4 font-medium text-gray-700">
                      {{ 'Translation Value' | translate }}
                    </th>
                    <th class="text-center py-3 px-4 font-medium text-gray-700 w-20">
                      {{ 'Status' | translate }}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let item of filteredTranslations; trackBy: trackByKey"
                    class="border-b border-gray-100 hover:bg-gray-50" [class.bg-yellow-50]="item.isModified">

                    <!-- Translation Key -->
                    <td class="py-3 px-4">
                      <div class="font-mono text-sm text-gray-800">{{ item.key }}</div>
                      <div class="text-xs text-gray-500 mt-1">
                        {{ item.key.split('.')[0] }} category
                      </div>
                    </td>

                    <!-- Translation Value -->
                    <td class="py-3 px-4">
                      <textarea [(ngModel)]="item.value" (ngModelChange)="onTranslationChange(item)"
                        class="form-textarea w-full min-h-[60px] resize-y" [class.border-orange-300]="item.isModified"
                        [class.bg-orange-50]="item.isModified" placeholder="{{ 'Enter translation...' | translate }}">
              </textarea>
                    </td>

                    <!-- Status -->
                    <td class="py-3 px-4 text-center">
                      <span *ngIf="item.isModified"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        <fa-icon [icon]="['fas', 'edit']" class="mr-1"></fa-icon>
                        {{ 'Modified' | translate }}
                      </span>
                      <span *ngIf="!item.isModified"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <fa-icon [icon]="['fas', 'check']" class="mr-1"></fa-icon>
                        {{ 'Saved' | translate }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>

            <!-- Mobile View - Card Layout -->
            <div class="block md:hidden">
              <div class="space-y-4">
                <div *ngFor="let item of filteredTranslations; trackBy: trackByKey"
                  class="bg-white border border-gray-200 rounded-lg shadow-sm p-4"
                  [class.border-orange-300]="item.isModified" [class.bg-orange-50]="item.isModified">

                  <!-- Mobile Card Header -->
                  <div class="flex items-start justify-between mb-3">
                    <div class="flex-1 min-w-0">
                      <div class="font-mono text-sm text-gray-800 break-all">{{ item.key }}</div>
                      <div class="text-xs text-gray-500 mt-1">
                        {{ item.key.split('.')[0] }} category
                      </div>
                    </div>

                    <!-- Status Badge -->
                    <div class="ml-3 flex-shrink-0">
                      <span *ngIf="item.isModified"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        <fa-icon [icon]="['fas', 'edit']" class="mr-1"></fa-icon>
                        {{ 'Modified' | translate }}
                      </span>
                      <span *ngIf="!item.isModified"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <fa-icon [icon]="['fas', 'check']" class="mr-1"></fa-icon>
                        {{ 'Saved' | translate }}
                      </span>
                    </div>
                  </div>

                  <!-- Translation Value Label -->
                  <div class="text-sm font-medium text-gray-700 mb-2">
                    {{ 'Translation Value' | translate }}
                  </div>

                  <!-- Translation Value Input -->
                  <textarea [(ngModel)]="item.value" (ngModelChange)="onTranslationChange(item)"
                    class="form-textarea w-full min-h-[80px] resize-y text-sm"
                    [class.border-orange-300]="item.isModified" [class.bg-orange-50]="item.isModified"
                    placeholder="{{ 'Enter translation...' | translate }}">
          </textarea>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div *ngIf="filteredTranslations.length === 0" class="empty-state">
              <div class="text-center py-12">
                <fa-icon [icon]="['fas', 'language']" class="text-4xl text-gray-300 mb-4"></fa-icon>
                <h3 class="text-lg font-medium text-gray-900 mb-2">{{ 'No translations found' | translate }}</h3>
                <p class="text-gray-500 text-sm px-4">{{ 'Try adjusting your search or filter criteria' | translate }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer Actions for Translations -->
        <div *ngIf="!isLoading" class="footer-actions mt-4">
          <div class="flex items-center justify-between py-4 border-t border-gray-200">
            <div class="text-sm text-gray-500">
              {{ 'Showing' | translate }} {{ filteredTranslations.length }} {{ 'of' | translate }} {{
              translations.length }} {{ 'translations' | translate }}
              <span *ngIf="lastModified" class="ml-4">
                {{ 'Last modified:' | translate }} {{ lastModified | date:'medium' }}
              </span>
            </div>

            <div class="flex items-center space-x-3">
              <button type="button" class="btn btn-outline" (click)="resetTranslations()"
                [disabled]="!hasUnsavedChanges()">
                {{ 'Reset All Changes' | translate }}
              </button>

              <button type="button" class="btn btn-primary" (click)="saveTranslations()"
                [disabled]="isSaving || !canSaveTranslations()">
                <fa-icon [icon]="['fas', 'save']" *ngIf="!isSaving"></fa-icon>
                <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isSaving"></fa-icon>
                {{ isSaving ? ('Saving...' | translate) : ('Save All Changes' | translate) }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Languages Tab -->
      <div *ngIf="activeTab === 'languages'" class="languages-tab">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">

          <!-- Predefined Languages -->
          <div class="predefined-languages-section">
            <div class="section-header">
              <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ 'Predefined Languages' | translate }}</h3>
              <p class="text-sm text-gray-600 mb-4">{{ 'Choose from our extensive list of predefined languages' |
                translate }}</p>

              <!-- Search for Predefined Languages -->
              <div class="relative mb-4">
                <fa-icon [icon]="['fas', 'search']"
                  class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></fa-icon>
                <input type="text" [(ngModel)]="predefinedLanguagesSearchTerm"
                  (ngModelChange)="onPredefinedLanguagesSearchChange()"
                  placeholder="{{ 'Search predefined languages...' | translate }}" class="form-input pl-10 w-full">
              </div>
            </div>

            <div class="language-grid">
              <div *ngFor="let lang of filteredPredefinedLanguages" class="language-card predefined"
                [class.selected]="selectedAvailableLanguages.includes(lang.code)"
                [class.is-default]="defaultLanguage === lang.code">
                <div class="language-info" (click)="toggleLanguageSelection(lang.code)">
                  <span [class]="lang.flag" class="flag-icon"></span>
                  <div class="language-details">
                    <div class="language-name">{{ lang.name }}</div>
                    <div class="language-code">{{ lang.code }}</div>
                    <div *ngIf="defaultLanguage === lang.code" class="default-indicator">
                      <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">{{ 'Default' | translate
                        }}</span>
                    </div>
                  </div>
                </div>
                <div class="language-actions">
                  <button *ngIf="selectedAvailableLanguages.includes(lang.code) && defaultLanguage !== lang.code"
                    type="button" class="btn-sm btn-outline text-xs"
                    (click)="setDefaultLanguage(lang.code); $event.stopPropagation()" title="Set as Default">
                    {{ 'Set Default' | translate }}
                  </button>
                  <fa-icon *ngIf="selectedAvailableLanguages.includes(lang.code)" [icon]="['fas', 'check-circle']"
                    class="text-green-500 ml-2"></fa-icon>
                  <fa-icon *ngIf="!selectedAvailableLanguages.includes(lang.code)" [icon]="['fas', 'circle']"
                    class="text-gray-400 ml-2"></fa-icon>
                </div>
              </div>
            </div>
          </div>

          <!-- Custom Languages -->
          <div class="custom-languages-section">
            <div class="section-header">
              <div class="flex items-center justify-between mb-4">
                <div>
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ 'Custom Languages' | translate }}</h3>
                  <p class="text-sm text-gray-600">{{ 'Create your own custom languages (e.g., "Vietnam 2", "English
                    US")' | translate }}</p>
                </div>
                <button type="button" class="btn btn-primary" (click)="openCustomLanguageForm()">
                  <fa-icon [icon]="['fas', 'plus']"></fa-icon>
                  {{ 'Add Custom Language' | translate }}
                </button>
              </div>
            </div>

            <div class="custom-language-list">
              <div *ngFor="let lang of customLanguages" class="language-card custom"
                [class.selected]="selectedAvailableLanguages.includes(lang.language_code)"
                [class.inactive]="!lang.active" [class.is-default]="defaultLanguage === lang.language_code">
                <div class="language-info" (click)="toggleLanguageSelection(lang.language_code)">
                  <span [class]="lang.flag_class || 'fi fi-xx'" class="flag-icon"></span>
                  <div class="language-details">
                    <div class="language-name">{{ lang.language_name }}</div>
                    <div class="language-code">{{ lang.language_code }}</div>
                    <div class="language-description" *ngIf="lang.description">{{ lang.description }}</div>
                    <div *ngIf="defaultLanguage === lang.language_code" class="default-indicator">
                      <span class="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">{{ 'Default' | translate
                        }}</span>
                    </div>
                  </div>
                </div>
                <div class="language-actions">
                  <button
                    *ngIf="selectedAvailableLanguages.includes(lang.language_code) && defaultLanguage !== lang.language_code"
                    type="button" class="btn-sm btn-outline text-xs"
                    (click)="setDefaultLanguage(lang.language_code); $event.stopPropagation()" title="Set as Default">
                    {{ 'Set Default' | translate }}
                  </button>
                  <button type="button" class="btn-icon" (click)="editCustomLanguage(lang); $event.stopPropagation()"
                    title="Edit">
                    <fa-icon [icon]="['fas', 'edit']"></fa-icon>
                  </button>
                  <button type="button" class="btn-icon text-red-600"
                    (click)="deleteCustomLanguage(lang); $event.stopPropagation()" title="Delete">
                    <fa-icon [icon]="['fas', 'trash']"></fa-icon>
                  </button>
                  <fa-icon *ngIf="selectedAvailableLanguages.includes(lang.language_code)"
                    [icon]="['fas', 'check-circle']" class="text-green-500 ml-2"></fa-icon>
                  <fa-icon *ngIf="!selectedAvailableLanguages.includes(lang.language_code)" [icon]="['fas', 'circle']"
                    class="text-gray-400 ml-2"></fa-icon>
                </div>
              </div>

              <div *ngIf="customLanguages.length === 0" class="empty-state">
                <div class="text-center py-8">
                  <fa-icon [icon]="['fas', 'plus-circle']" class="text-4xl text-gray-300 mb-4"></fa-icon>
                  <h3 class="text-lg font-medium text-gray-900 mb-2">{{ 'No custom languages' | translate }}</h3>
                  <p class="text-gray-500 mb-4">{{ 'Create your first custom language to get started' | translate }}</p>
                  <button type="button" class="btn btn-primary" (click)="openCustomLanguageForm()">
                    <fa-icon [icon]="['fas', 'plus']"></fa-icon>
                    {{ 'Add Custom Language' | translate }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Save Settings -->
        <div class="settings-actions mt-8">
          <button type="button" class="btn btn-primary" (click)="saveLanguageSettings()"
            [disabled]="isSaving || selectedAvailableLanguages.length === 0">
            <fa-icon [icon]="['fas', 'save']" *ngIf="!isSaving"></fa-icon>
            <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isSaving"></fa-icon>
            {{ isSaving ? ('Saving...' | translate) : ('Save Language Configuration' | translate) }}
          </button>
        </div>
      </div>
    </div>

    <!-- Custom Language Form Modal -->
    <div *ngIf="showCustomLanguageForm" class="modal-overlay" (click)="closeCustomLanguageForm()">
      <div class="modal-container" (click)="$event.stopPropagation()">
        <div class="modal-header">
          <h2 class="modal-title">
            {{ editingCustomLanguage ? ('Edit Custom Language' | translate) : ('Add Custom Language' | translate) }}
          </h2>
          <button class="close-button" (click)="closeCustomLanguageForm()">
            <fa-icon [icon]="['fas', 'times']"></fa-icon>
          </button>
        </div>

        <div class="modal-content">
          <form (ngSubmit)="saveCustomLanguage()" #customLangForm="ngForm">
            <div class="form-group">
              <label class="form-label">{{ 'Language Code' | translate }} *</label>
              <input type="text" [(ngModel)]="customLanguageForm.language_code" name="language_code" class="form-input"
                placeholder="e.g., vi2, en-us, custom1" required pattern="^[a-z0-9_-]+$"
                [disabled]="!!editingCustomLanguage">
              <div class="form-help">{{ 'Use lowercase letters, numbers, underscores, and hyphens only' | translate }}
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">{{ 'Language Name' | translate }} *</label>
              <input type="text" [(ngModel)]="customLanguageForm.language_name" name="language_name" class="form-input"
                placeholder="e.g., Việt Nam 2, English (US), Custom Language" required>
            </div>

            <div class="form-group">
              <label class="form-label">{{ 'Flag Class' | translate }}</label>
              <input type="text" [(ngModel)]="customLanguageForm.flag_class" name="flag_class" class="form-input"
                placeholder="e.g., fi fi-vn, fi fi-us">
              <div class="form-help">{{ 'CSS class for flag icon (optional)' | translate }}</div>
            </div>

            <div class="form-group">
              <label class="form-label">{{ 'Description' | translate }}</label>
              <textarea [(ngModel)]="customLanguageForm.description" name="description" class="form-textarea" rows="3"
                placeholder="{{ 'Optional description for this custom language' | translate }}">
            </textarea>
            </div>

            <div class="form-group">
              <label class="flex items-center">
                <input type="checkbox" [(ngModel)]="customLanguageForm.active" name="active" class="form-checkbox">
                <span class="ml-2">{{ 'Active' | translate }}</span>
              </label>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" (click)="closeCustomLanguageForm()" [disabled]="isSaving">
            {{ 'Cancel' | translate }}
          </button>
          <button type="button" class="btn btn-primary" (click)="saveCustomLanguage()"
            [disabled]="isSaving || !customLanguageForm.language_code || !customLanguageForm.language_name">
            <fa-icon [icon]="['fas', 'spinner']" [spin]="true" *ngIf="isSaving"></fa-icon>
            {{ isSaving ? ('Saving...' | translate) : (editingCustomLanguage ? ('Update' | translate) : ('Create' |
            translate)) }}
          </button>
        </div>
      </div>
    </div>

  </div>
</div>