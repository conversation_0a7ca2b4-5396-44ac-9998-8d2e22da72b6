import { CommonModule } from '@angular/common';
import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IconsModule } from '../../../icons/icons.module';
import { TicketService } from '../../../core/services/ticket.service';
import { TicketRes } from '../../../model/response/ticket-res.model';
import { Subscription } from 'rxjs';
import { AdminSupportDetailComponent } from './admin-support-detail/admin-support-detail.component';
import { TicketFilter } from '../../../model/request/ticket-filter.model';
import { DateRangePickerComponent } from '../../common/date-range-picker/date-range-picker.component';
import { TicketStatusComponent } from '../../common/ticket-status/ticket-status.component';
import { AdminMenuComponent, AdminMenuItem } from '../../../components/common/admin-menu/admin-menu.component';
import { LoadingService } from '../../../core/services/loading.service';
import { ToastService } from '../../../core/services/toast.service';
import { TimezoneService } from '../../../core/services/timezone.service';
import { TimezonePipe } from '../../../shared/pipes/timezone.pipe';
import { DateHelper } from '../../../core/common/date-helper';


@Component({
  selector: 'app-admin-support',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    IconsModule,
    AdminSupportDetailComponent,
    DateRangePickerComponent,
    TicketStatusComponent,
    AdminMenuComponent,
    TimezonePipe
],
  templateUrl: './admin-support.component.html',
  styleUrls: ['./admin-support.component.css', '../common/admin-menu.css']
})
export class AdminSupportComponent implements OnInit, OnDestroy {
  tickets: TicketRes[] = [];
  isLoading = false;
  pagination = {
    pageNumber: 0,
    pageSize: 20,
    totalElements: 0,
    totalPages: 0
  };
  private subscriptions: Subscription[] = [];
  activeActionMenu: number | null = null;
  selectedTicketId: number | null = null;
  viewMode: 'table' | 'card' = 'table'; // Default to table view, toggle to card view for mobile

  // Filter properties
  filter: TicketFilter = {};
  showFilters = false;
  statusFilters = [
    { value: 'all', label: 'All', active: true },
    { value: 'Pending', label: 'Pending', active: false },
    { value: 'Accept', label: 'Accept', active: false },
    { value: 'Closed', label: 'Closed', active: false },
    { value: 'Solved', label: 'Solved', active: false }
  ];
  selectedStatus = 'all';
  searchTerm: string = '';
  dateRange: { startDate: Date | null, endDate: Date | null } = {
    startDate: null,
    endDate: null
  };

  constructor(
    private ticketService: TicketService,
    private loadingService: LoadingService,
    private toastService: ToastService,
    public timezoneService: TimezoneService
  ) {}

  ngOnInit(): void {
    // Subscribe to tickets
    this.subscriptions.push(
      this.ticketService.tickets$.subscribe(tickets => {
        this.tickets = tickets;
      })
    );

    // Subscribe to loading state
    this.subscriptions.push(
      this.ticketService.loading$.subscribe(loading => {
        this.isLoading = loading;
        if (loading) {
          this.loadingService.show('Loading tickets...');
        } else {
          this.loadingService.hide();
        }
      })
    );

    // Subscribe to pagination
    this.subscriptions.push(
      this.ticketService.pagination$.subscribe(pagination => {
        this.pagination = pagination;
      })
    );

    // Detect mobile device and set view mode accordingly
    this.detectMobileDevice();

    // Listen for window resize events to update view mode
    window.addEventListener('resize', () => {
      this.detectMobileDevice();
    });

    // Load initial tickets
    this.loadTickets();
  }

  /**
   * Detect if the device is mobile and set the view mode accordingly
   */
  detectMobileDevice(): void {
    // Check if screen width is less than 768px (typical mobile breakpoint)
    if (window.innerWidth < 768) {
      this.viewMode = 'card';
    } else {
      this.viewMode = 'table';
    }
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.subscriptions.forEach(sub => sub.unsubscribe());

    // Remove resize event listener
    window.removeEventListener('resize', () => {
      this.detectMobileDevice();
    });

    // Make sure to hide the loading indicator when component is destroyed
    this.loadingService.hide();
  }

  loadTickets(page: number = 0): void {
    // Use the filter API for admin
    this.ticketService.getTicketsByFilters(this.filter, page).subscribe({
      error: (error) => {
        console.error('Error loading tickets:', error);
        this.toastService.showError(error?.message || 'Failed to load tickets');
      }
    });
  }

  navigateTo(id: number) {
    // Instead of navigating to a new page, open the popup
    this.selectedTicketId = id;
  }

  closeTicketDetail() {
    this.selectedTicketId = null;
  }

  changePage(page: number): void {
    if (page >= 0 && page < this.pagination.totalPages) {
      this.loadTickets(page);
    }
  }

  onSearchButton(): void {
    // Trim whitespace from search term
    const trimmedSearchTerm = this.searchTerm.trim();
    console.log('Searching for:', trimmedSearchTerm);

    if (trimmedSearchTerm) {
      this.filter.userName = trimmedSearchTerm;
    } else {
      this.filter.userName = undefined;
    }
    this.loadTickets();
  }

  toggleFiltersPanel(): void {
    this.showFilters = !this.showFilters;
  }

  toggleFilter(filter: any) {
    this.statusFilters.forEach(f => f.active = false);
    filter.active = true;
    this.selectedStatus = filter.value;

    // Update the filter object
    if (this.selectedStatus !== 'all') {
      this.filter.status = this.selectedStatus;
    } else {
      this.filter.status = undefined;
    }

    // Load tickets with the updated filter
    this.loadTickets();
  }

  applyFilters(): void {
    // Update date range
    if (this.dateRange) {
      if (this.dateRange.startDate) {
        this.filter.startDate = DateHelper.formatDateTime(this.dateRange.startDate);
      } else {
        this.filter.startDate = undefined;
      }

      if (this.dateRange.endDate) {
        this.filter.endDate = DateHelper.formatDateTime(this.dateRange.endDate);
      } else {
        this.filter.endDate = undefined;
      }
    }

    // Load tickets with the updated filter
    this.loadTickets();
  }

  onDateRangeChanged(range: { startDate: Date | null, endDate: Date | null }): void {
    this.dateRange = range;

    if (range.startDate) {
      this.filter.startDate = DateHelper.formatDateTime(range.startDate);
    } else {
      this.filter.startDate = undefined;
    }

    if (range.endDate) {
      this.filter.endDate = DateHelper.formatDateTime(range.endDate);
    } else {
      this.filter.endDate = undefined;
    }
  }

  resetFilters(): void {
    this.filter = {};

    // Reset status filters
    this.statusFilters.forEach(f => f.active = false);
    this.statusFilters[0].active = true; // Set 'All' as active
    this.selectedStatus = 'all';

    // Reset date range to null values
    this.dateRange = {
      startDate: null,
      endDate: null
    };

    this.loadTickets();
  }




  menuPosition = { left: 0, top: 0 };
  openActionMenu(event: MouseEvent, ticketId: number) {
    event.stopPropagation();

    // Close if already open for this ticket
    if (this.activeActionMenu === ticketId) {
      this.closeActionMenu();
      return;
    }

    // Always set to the new ticketId, which will close any previously open menu
    this.activeActionMenu = ticketId;

    // Position the menu relative to the button, accounting for scroll position
    if (event) {
      const button = event.currentTarget as HTMLElement;
      const rect = button.getBoundingClientRect();

      // Calculate position for the menu
      // Use the button's position on the screen plus a small offset
      this.menuPosition = {
        left: rect.left + window.scrollX,
        top: rect.bottom + window.scrollY
      };

      // Adjust position for mobile view
      if (window.innerWidth < 768) {
        // On mobile, position the menu below the button
        this.menuPosition = {
          left: Math.max(5, rect.left + window.scrollX - 150), // Ensure it's not off-screen to the left
          top: rect.bottom + window.scrollY + 5
        };
      }
    }

    console.log('Toggle menu for ticket:', ticketId, 'Active menu:', this.activeActionMenu);
  }

  closeActionMenu(): void {
    this.activeActionMenu = null;
  }

  // Action methods
  viewTicketDetails(ticketId: number): void {
    this.navigateTo(ticketId);
    this.closeActionMenu();
  }

  markAsAccepted(ticketId: number): void {
    console.log('Mark as accepted:', ticketId);
    this.updateTicketStatus(ticketId, 'Accept');
    this.closeActionMenu();
  }

  markAsSolved(ticketId: number): void {
    console.log('Mark as solved:', ticketId);
    this.updateTicketStatus(ticketId, 'Solved');
    this.closeActionMenu();
  }

  markAsClosed(ticketId: number): void {
    console.log('Mark as closed:', ticketId);
    this.updateTicketStatus(ticketId, 'Closed');
    this.closeActionMenu();
  }

  private updateTicketStatus(ticketId: number, status: string): void {
    // Call the API to update the ticket status
    this.ticketService.updateTicketStatus(ticketId, status).subscribe({
      next: (_) => {
        console.log(`Updated ticket ${ticketId} status to ${status}`);
        this.toastService.showSuccess(`Ticket status updated to ${status}`);
      },
      error: (error) => {
        console.error('Error updating ticket status:', error);
        this.toastService.showError(error?.message || 'Failed to update ticket status');
      }
    });
  }

  /**
   * Get menu items for a ticket
   */
  getTicketMenuItems(ticket: TicketRes): AdminMenuItem[] {
    const items: AdminMenuItem[] = [];

    // View details
    items.push({
      id: 'view-details',
      label: 'menu.ticket.view_details',
      icon: 'eye',
      iconColor: 'text-blue-500'
    });

    // Mark as accepted
    items.push({
      id: 'mark-accepted',
      label: 'menu.ticket.mark_accepted',
      icon: 'check',
      iconColor: 'text-blue-500'
    });

    // Mark as solved
    items.push({
      id: 'mark-solved',
      label: 'menu.ticket.mark_solved',
      icon: 'check-double',
      iconColor: 'text-green-500'
    });

    // Mark as closed
    items.push({
      id: 'mark-closed',
      label: 'menu.ticket.mark_closed',
      icon: 'times-circle',
      iconColor: 'text-red-500'
    });


    return items;
  }

  /**
   * Handle ticket menu item click
   */
  onTicketMenuItemClick(itemId: string, ticketId: number): void {
    switch (itemId) {
      case 'view-details':
        this.viewTicketDetails(ticketId);
        break;
      case 'mark-accepted':
        this.markAsAccepted(ticketId);
        break;
      case 'mark-solved':
        this.markAsSolved(ticketId);
        break;
      case 'mark-closed':
        this.markAsClosed(ticketId);
        break;

    }
  }
}
