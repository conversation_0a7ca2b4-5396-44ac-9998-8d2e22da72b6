import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { TranslateModule } from '@ngx-translate/core';
import { CdkDrag, CdkDropList, CdkDragPreview, CdkDragDrop, CdkDragStart, moveItemInArray } from '@angular/cdk/drag-drop';


import { ServiceLabelComponent } from "../../common/service-label/service-label.component";
import { SocialIconComponent } from "../../common/social-icon/social-icon.component";


import { AdminMenuComponent } from "../../common/admin-menu/admin-menu.component";
import { AdminServicesHeaderComponent } from "./admin-services-header/admin-services-header.component";
import { CurrencyConvertPipe } from '../../../core/pipes/currency-convert.pipe';

// Popup Components
import { NewServiceComponent } from '../../popup/new-service/new-service.component';
import { AddPlatformLightComponent } from '../../popup/add-platform-light/add-platform-light.component';
import { PlatformManagementComponent } from '../../platform-management/platform-management.component';
import { CategorySelectionComponent } from '../../popup/category-selection/category-selection.component';
import { NewCategoryComponent } from '../../popup/new-category/new-category.component';
import { NewPricesComponent } from '../../popup/new-prices/new-prices.component';
import { NewSpecialPricesComponent } from '../../popup/new-special-prices/new-special-prices.component';
import { SpecialPricesUserComponent } from '../../popup/special-prices-user/special-prices-user.component';
import { SpecialPricesServiceComponent } from '../../popup/special-prices-service/special-prices-service.component';
import { ImportServicesComponent } from '../../popup/import-services/import-services.component';
import { ResourcesComponent } from '../../popup/resources/resources.component';
import { DeleteConfirmationComponent } from '../../settings/delete-confirmation/delete-confirmation.component';

// Services
import { AdminServiceService } from '../../../core/services/admin-service.service';
import { CategoryService } from '../../../core/services/category.service';
import { ServiceManagementService } from '../../../core/services/service-management.service';
import { FilterService } from '../../../core/services/filter.service';
import { SelectionService } from '../../../core/services/selection.service';
import { UIStateService } from '../../../core/services/ui-state.service';
import { PlatformService } from '../../../core/services/platform.service';
import { LoadingService } from '../../../core/services/loading.service';
import { ToastService } from '../../../core/services/toast.service';

// Models
import { SuperPlatformRes } from '../../../model/response/super-platform.model';
import { SuperCategoryRes } from '../../../model/response/super-category.model';
import { SuperGeneralSvRes } from '../../../model/response/super-general-sv.model';
import { ExtendedCategoryRes } from '../../../model/extended/extended-category.model';
import { SpecialPriceRes } from '../../../model/response/special-price-res.model';
import { ProviderRes } from '../../../model/response/provider-res.model';
import { AddType } from '../../../constant/add-type';
import { SecondsToDurationPipe } from '../../../core/pipes/seconds-to-duration.pipe';



@Component({
  selector: 'app-admin-services-v2',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    FontAwesomeModule,
    TranslateModule,
    AdminServicesHeaderComponent,
    ServiceLabelComponent,
    SocialIconComponent,
    SecondsToDurationPipe,
    NewServiceComponent,
    AddPlatformLightComponent,
    PlatformManagementComponent,
    CategorySelectionComponent,
    NewCategoryComponent,
    NewPricesComponent,
    NewSpecialPricesComponent,
    SpecialPricesUserComponent,
    SpecialPricesServiceComponent,
    ImportServicesComponent,
    ResourcesComponent,
    DeleteConfirmationComponent,
    AdminMenuComponent,
    CurrencyConvertPipe,
    CdkDrag,
    CdkDropList,
    CdkDragPreview
  ],
  templateUrl: './admin-services-v2.component.html',
  styleUrls: ['./admin-services-v2.component.css', '../common/admin-menu.css']
})
export class AdminServicesV2Component implements OnInit, OnDestroy {
  allPlatforms: SuperPlatformRes[] = [];

  // View mode for mobile/desktop
  viewMode: 'table' | 'card' = 'table';

  // Category platform dropdown properties
  categoryPlatformSelections: Map<number, Set<number>> = new Map<number, Set<number>>();
  get showCategoryPlatformDropdown(): boolean { return this.uiStateService.showCategoryPlatformDropdown; }
  get selectedCategoryForPlatform(): ExtendedCategoryRes | null { return this.uiStateService.selectedCategoryForPlatform; }
  get categoryPlatformMenuPosition(): { top: number, left: number } { return this.uiStateService.categoryPlatformMenuPosition; }

  // Category options for dropdown
  categoryOptions: any[] = [];
  selectedCategory: any = null;

  // API Provider options for dropdown
  apiProviderOptions: any[] = [];
  selectedApiProvider: any = null;
  allApiProviders: ProviderRes[] = [];

  // Display categories and current category
  displayCategories: ExtendedCategoryRes[] = [];
  currentCategory: ExtendedCategoryRes | null = null;

  // Unfiltered state for reset functionality
  unfilteredDisplayCategories: ExtendedCategoryRes[] = [];
  unfilteredCurrentCategory: ExtendedCategoryRes | null = null;

  // Filter state
  isFilterApplied = false;
  searchTerm = '';

  // Resources modal
  showResourcesModal = false;

  // Delete confirmation
  showDeleteConfirmation = false;
  serviceToDelete: SuperGeneralSvRes | null = null;
  isDeleting = false;

  // Bulk delete confirmation
  showBulkDeleteConfirmation = false;
  bulkDeleteType: 'services' | 'custom-prices' | 'category' = 'services';
  bulkDeleteMessage = '';
  bulkDeleteCount = 0;

  // Category delete confirmation
  showCategoryDeleteConfirmation = false;
  categoryToDelete: ExtendedCategoryRes | null = null;
  isCategoryDeleting = false;

  // Drag and drop state
  draggedCategory: ExtendedCategoryRes | null = null;
  draggedCategoryIndex: number = -1;
  dragOverIndex: number = -1;
  dragOverCategoryIndex: number = -1;
  isDragging: boolean = false;

  // Category move mode
  isCategoryMoveMode: boolean = false;

  constructor(
    private adminServiceService: AdminServiceService,
    private categoryService: CategoryService,
    private serviceManagementService: ServiceManagementService,
    private filterService: FilterService,
    public selectionService: SelectionService,
    public uiStateService: UIStateService,
    private platformService: PlatformService,
    private loadingService: LoadingService,
    private toastService: ToastService
  ) {}

  // Getters for UI state
  get showModal(): boolean { return this.uiStateService.showModal; }
  get showAddServiceV2Modal(): boolean { return this.uiStateService.showAddServiceV2Modal; }
  get showAddPlatformLightModal(): boolean { return this.uiStateService.showAddPlatformLightModal; }
  get showPlatformManagementModal(): boolean { return this.uiStateService.showPlatformManagementModal; }
  get showCategorySelectionModal(): boolean { return this.uiStateService.showCategorySelectionModal; }
  get showNewCategoryModal(): boolean { return this.uiStateService.showNewCategoryModal; }
  get showNewPricesModal(): boolean { return this.uiStateService.showNewPricesModal; }
  get showNewSpecialPricesModal(): boolean { return this.uiStateService.showNewSpecialPricesModal; }
  get showSpecialPricesUserModal(): boolean { return this.uiStateService.showSpecialPricesUserModal; }
  get showSpecialPricesServiceModal(): boolean { return this.uiStateService.showSpecialPricesServiceModal; }
  get showImportServicesModal(): boolean { return this.uiStateService.showImportServicesModal; }
  get showNewServiceModal(): boolean { return this.uiStateService.showNewServiceModal; }

  // Selected items for actions
  get selectedServiceForAction(): SuperGeneralSvRes | null { return this.uiStateService.selectedServiceForAction; }
  get selectedCategoryForAction(): ExtendedCategoryRes | null { return this.uiStateService.selectedCategoryForAction; }
  get categoryToEdit(): any | null { return this.uiStateService.categoryToEdit; }
  set categoryToEdit(value: any | null) { this.uiStateService.categoryToEdit = value; }
  get specialPriceToEdit(): any | null { return this.uiStateService.specialPriceToEdit; }

  // Menu positions
  get menuPosition(): { top: number, left: number } { return this.uiStateService.menuPosition; }
  get categoryMenuPosition(): { top: number, left: number } { return this.uiStateService.categoryMenuPosition; }

  // Menu states
  get showBulkActionMenu(): boolean { return this.uiStateService.showBulkActionMenu; }
  get showServiceActionMenu(): boolean { return this.uiStateService.showServiceActionMenu; }
  get showCategoryActionMenu(): boolean { return this.uiStateService.showCategoryActionMenu; }

  // Loading states
  get loading(): boolean { return this.uiStateService.loading; }
  get loadingBulkOperation(): boolean { return this.uiStateService.loadingBulkOperation; }
  get bulkOperationMessage(): string { return this.uiStateService.bulkOperationMessage; }

  ngOnInit(): void {
    this.loadApiProviders();
    this.loadPlatforms();
    this.detectMobileDevice();

    // Listen for window resize events to update view mode
    window.addEventListener('resize', () => {
      this.detectMobileDevice();
    });

    // Close menus when scrolling
    window.addEventListener('scroll', () => {
      this.closeAllMenus();
    }, true);
  }

  ngOnDestroy(): void {
    // Remove event listeners
    window.removeEventListener('resize', () => {
      this.detectMobileDevice();
    });

    window.removeEventListener('scroll', () => {
      this.closeAllMenus();
    }, true);
  }

  // Close all menus
  closeAllMenus(): void {
    this.uiStateService.closeBulkActionMenu();
    this.uiStateService.closeServiceActionMenu();
    this.uiStateService.closeCategoryActionMenu();
  }

  /**
   * Detect if the device is mobile and set the view mode accordingly
   */
  detectMobileDevice(): void {
    // Check if screen width is less than 768px (typical mobile breakpoint)
    if (window.innerWidth < 768) {
      this.viewMode = 'card';
    } else {
      this.viewMode = 'table';
    }
  }

  loadApiProviders(): void {
    this.adminServiceService.getProviders().subscribe({
      next: (providers) => {
        this.allApiProviders = providers;

        // Create a default "All Providers" option
        const allProvidersOption = {
          id: 'all',
          label: 'filter.all_providers',
          sort: 0,
          icon: ''
        };

        // Create API provider options
        this.apiProviderOptions = [allProvidersOption];

        // Add individual providers
        providers.forEach(provider => {
          this.apiProviderOptions.push({
            id: provider.id,
            label: provider.name || provider.url,
            sort: 0,
            icon: ''
          });
        });

        // Set default selected API provider to "All Providers"
        this.selectedApiProvider = allProvidersOption;
      },
      error: (error) => {
        console.error('Error loading API providers:', error);
        this.toastService.showError('Failed to load API providers');
      }
    });
  }

  loadPlatforms(): void {
    this.uiStateService.setLoading(true, 'Loading platforms and services...');
    this.loadingService.show('Loading platforms and services...');

    // Use PlatformService to load platforms with services
    this.platformService.loadPlatformsWithServices(
      (platforms) => {
        // Store all platforms data
        this.allPlatforms = platforms;

        // Create a default "All Categories" option
        const allCategoriesOption = {
          id: 'all',
          label: 'filter.all_categories',
          sort: 0,
          icon: '' // No icon
        };

        // Create category options from platforms
        this.categoryOptions = [allCategoriesOption];

        // Add individual categories from all platforms
        platforms.forEach(platform => {
          if (platform.categories && platform.categories.length > 0) {
            platform.categories.forEach(category => {
              this.categoryOptions.push({
                id: category.id,
                label: category.name,
                sort: category.sort || 0,
                icon: platform.icon || ''
              });
            });
          }
        });

        // Sort category options by sort field
        this.categoryOptions.sort((a, b) => a.sort - b.sort);

        // Set default selected category to "All Categories"
        this.selectedCategory = allCategoriesOption;

        // Process platforms data for display
        this.processDisplayCategories(platforms);

        this.uiStateService.setLoading(false);
        this.loadingService.hide();
      },
      (error) => {
        console.error('Error loading platforms:', error);
        this.uiStateService.setLoading(false);
        this.loadingService.hide();
        this.toastService.showError('Failed to load platforms and services');
      }
    );
  }

  processDisplayCategories(platforms: SuperPlatformRes[]): void {
    this.displayCategories = [];

    platforms.forEach(platform => {
      if (platform.categories && platform.categories.length > 0) {
        platform.categories.forEach(category => {
          const extendedCategory: ExtendedCategoryRes = {
            ...category,
            // platformId: platform.id,
            platformName: platform.name,
            platformIcon: platform.icon,
            isAllPlatforms: false,
            isAllCategories: false,
            hide: false,
            services: category.services || []
          };

          this.displayCategories.push(extendedCategory);
        });
      }
    });

    // Sort categories by sort field
    this.displayCategories.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  }

  // Category selection methods
  onCategorySelected(category: any): void {
    this.selectedCategory = category;

    // Apply combined filter (category + search + API provider) instead of resetting
    this.applyCombinedFilter();
  }

  // API Provider selection methods
  onApiProviderSelected(apiProvider: any): void {
    this.selectedApiProvider = apiProvider;

    // Apply combined filter (category + search + API provider) instead of resetting
    this.applyCombinedFilter();
  }

  // Filter methods
  applyFilter(searchText: string): void {
    this.searchTerm = searchText;
    this.applyCombinedFilter();
  }

  // Combined filter method that applies category selection, search, and API provider filtering
  applyCombinedFilter(): void {
    const searchTerm = this.searchTerm.toLowerCase().trim();

    // Check if any filter is being applied
    const hasFilter = searchTerm !== '' ||
                     (this.selectedCategory && this.selectedCategory.id !== 'all') ||
                     (this.selectedApiProvider && this.selectedApiProvider.id !== 'all');

    // Exit move mode immediately when filter is applied
    if (hasFilter && this.isCategoryMoveMode) {
      this.isCategoryMoveMode = false;
    }

    // Save unfiltered state if this is the first filter operation
    if (!this.isFilterApplied && hasFilter) {
      // If we don't have unfiltered data yet, save current state
      if (this.unfilteredDisplayCategories.length === 0) {
        this.processDisplayCategories(this.allPlatforms);
        this.unfilteredDisplayCategories = [...this.displayCategories];
        this.unfilteredCurrentCategory = this.currentCategory;
      }
      this.isFilterApplied = true;
    } else if (!hasFilter) {
      // Reset filter state when no filter is applied
      this.isFilterApplied = false;
      this.unfilteredDisplayCategories = [];
      this.unfilteredCurrentCategory = null;
    }

    // Start with the original unfiltered data (all categories)
    let categoriesToFilter: ExtendedCategoryRes[] = [];

    // Get all categories from platforms
    this.allPlatforms.forEach(platform => {
      if (platform.categories && platform.categories.length > 0) {
        platform.categories.forEach(category => {
          const extendedCategory: ExtendedCategoryRes = {
            ...category,
            platformName: platform.name,
            platformIcon: platform.icon,
            isAllPlatforms: false,
            isAllCategories: false,
            hide: false,
            services: category.services || []
          };
          categoriesToFilter.push(extendedCategory);
        });
      }
    });

    let currentCategoryToFilter = null;

    // Apply category filter first (if not "All Categories")
    if (this.selectedCategory && this.selectedCategory.id !== 'all') {
      const foundCategory = categoriesToFilter.find(cat => cat.id === this.selectedCategory.id);
      if (foundCategory) {
        categoriesToFilter = [foundCategory];
        currentCategoryToFilter = foundCategory;
      }
    }

    // Then apply search filter if there's a search term
    if (searchTerm) {
      // Filter display categories by service name or ID
      categoriesToFilter = categoriesToFilter
        .map(category => ({
          ...category,
          services: category.services.filter(service =>
            service.name.toLowerCase().includes(searchTerm) ||
            (service.api_service_id && service.api_service_id.toString().toLowerCase().includes(searchTerm)) ||
            service.id.toString().includes(searchTerm)
          )
        }))
        .filter(category => category.services.length > 0); // Only keep categories with matching services

      // Filter current category by service name or ID
      if (currentCategoryToFilter) {
        const filteredServices = currentCategoryToFilter.services.filter(service =>
          service.name.toLowerCase().includes(searchTerm) ||
          (service.api_service_id && service.api_service_id.toString().toLowerCase().includes(searchTerm)) ||
          service.id.toString().includes(searchTerm)
        );

        if (filteredServices.length > 0) {
          // Create a new category object with filtered services
          currentCategoryToFilter = {
            ...currentCategoryToFilter,
            services: filteredServices
          };
        } else {
          // No matching services in current category
          currentCategoryToFilter = null;
        }
      }
    }

    // Finally apply API provider filter if a specific provider is selected
    if (this.selectedApiProvider && this.selectedApiProvider.id !== 'all') {
      const selectedProviderId = this.selectedApiProvider.id;

      // Filter display categories by API provider
      categoriesToFilter = categoriesToFilter
        .map(category => ({
          ...category,
          services: category.services.filter(service =>
            service.api_provider && service.api_provider.id === selectedProviderId
          )
        }))
        .filter(category => category.services.length > 0); // Only keep categories with matching services

      // Filter current category by API provider
      if (currentCategoryToFilter) {
        const filteredServices = currentCategoryToFilter.services.filter(service =>
          service.api_provider && service.api_provider.id === selectedProviderId
        );

        if (filteredServices.length > 0) {
          // Create a new category object with filtered services
          currentCategoryToFilter = {
            ...currentCategoryToFilter,
            services: filteredServices
          };
        } else {
          // No matching services in current category
          currentCategoryToFilter = null;
        }
      }
    }

    // Update the display
    this.displayCategories = categoriesToFilter;
    this.currentCategory = currentCategoryToFilter;

    // Update move mode based on category state
    this.checkAutoMoveMode();
  }

  resetFilter(): void {
    // Reset search term, category selection, and API provider selection
    this.searchTerm = '';
    this.selectedCategory = this.categoryOptions.find(cat => cat.id === 'all') || null;
    this.selectedApiProvider = this.apiProviderOptions.find(provider => provider.id === 'all') || null;
    this.isFilterApplied = false;

    // Show all categories without any filter
    this.currentCategory = null;
    this.processDisplayCategories(this.allPlatforms);

    // Clear unfiltered state
    this.unfilteredDisplayCategories = [];
    this.unfilteredCurrentCategory = null;

    // Update move mode based on category state
    this.checkAutoMoveMode();
  }

  // Service selection methods
  isServiceSelected(service: SuperGeneralSvRes): boolean {
    return this.selectionService.isServiceSelected(service);
  }

  toggleServiceSelection(service: SuperGeneralSvRes, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleServiceSelection(service, checkbox.checked);
  }

  areAllServicesSelected(): boolean {
    return this.selectionService.areAllServicesSelected(this.displayCategories, this.currentCategory);
  }

  toggleAllServices(event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleAllServices(this.displayCategories, this.currentCategory, checkbox.checked);
  }

  hasSelectedServices(): boolean {
    return this.selectionService.hasSelectedServices();
  }

  getSelectedServicesCount(): number {
    return this.selectionService.getSelectedServicesCount();
  }

  getAllServices(): SuperGeneralSvRes[] {
    const allServices: SuperGeneralSvRes[] = [];

    if (this.currentCategory) {
      allServices.push(...this.currentCategory.services);
    } else {
      this.displayCategories.forEach(category => {
        allServices.push(...category.services);
      });
    }

    return allServices;
  }

  // Category selection methods
  isCategoryFullySelected(category: ExtendedCategoryRes): boolean {
    return this.selectionService.isCategoryFullySelected(category);
  }

  toggleCategorySelection(category: ExtendedCategoryRes, event: Event): void {
    const checkbox = event.target as HTMLInputElement;
    this.selectionService.toggleCategorySelection(category, checkbox.checked);
  }

  // Service status methods
  isServiceEnabled(service: SuperGeneralSvRes): boolean {
    return service.status === 'ACTIVATED';
  }

  toggleServiceStatus(service: SuperGeneralSvRes): void {
    if (this.isServiceEnabled(service)) {
      this.adminServiceService.deactivateService(service.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Service deactivated successfully');
          this.loadPlatforms();
        },
        error: (error) => {
          console.error('Error deactivating service:', error);
          this.toastService.showError('Failed to deactivate service');
        }
      });
    } else {
      this.adminServiceService.activateService(service.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Service activated successfully');
          this.loadPlatforms();
        },
        error: (error) => {
          console.error('Error activating service:', error);
          this.toastService.showError('Failed to activate service');
        }
      });
    }
  }

  // Modal methods
  openModal(): void {
    this.uiStateService.openModal();
  }

  closeModal(): void {
    this.uiStateService.closeModal();
  }

  closeAddServiceV2Modal(): void {
    this.uiStateService.closeAddServiceV2Modal();
    this.loadPlatforms();
  }

  closeAddPlatformLightModal(): void {
    this.uiStateService.closeAddPlatformLightModal();
    this.loadPlatforms();
  }

  closePlatformManagementModal(): void {
    this.uiStateService.closePlatformManagementModal();
    this.loadPlatforms();
  }

  closeCategorySelectionModal(): void {
    this.uiStateService.closeCategorySelectionModal();
    // Reload platforms to reflect any category changes
    this.loadPlatforms();
  }

  closeNewCategoryModal(): void {
    this.uiStateService.closeNewCategoryModal();
    this.loadPlatforms();
  }

  closeNewPricesModal(): void {
    this.uiStateService.closeNewPricesModal();
    this.loadPlatforms();
  }

  closeNewSpecialPricesModal(): void {
    this.uiStateService.closeNewSpecialPricesModal();
    this.loadPlatforms();
  }

  closeSpecialPricesUserModal(): void {
    this.uiStateService.closeSpecialPricesUserModal();
    this.loadPlatforms();
  }

  closeSpecialPricesServiceModal(): void {
    this.uiStateService.closeSpecialPricesServiceModal();
    this.loadPlatforms();
  }

  closeImportServicesModal(): void {
    this.uiStateService.closeImportServicesModal();
    this.loadPlatforms();
  }

  // Action button methods
  addService(): void {
    this.showResourcesModal = true;
  }

  importServices(): void {
    this.uiStateService.openImportServicesModal();
  }

  openNewCategoryModal(): void {
    this.categoryToEdit = null;
    this.uiStateService.openNewCategoryModal();
  }

  openPlatformManagement(): void {
    this.uiStateService.openPlatformManagementModal();
  }

  // Menu item methods
  getBulkActionMenuItems(): any[] {
    const items = [];

    // Disable all
    items.push({
      id: 'disable-all',
      label: 'Disable all',
      icon: 'ban',
      iconColor: 'text-gray-500'
    });

    // Enable all
    items.push({
      id: 'enable-all',
      label: 'Enable all',
      icon: 'check',
      iconColor: 'text-green-500'
    });

    // Change category
    items.push({
      id: 'change-category',
      label: 'Change category',
      icon: 'folder',
      iconColor: 'text-blue-500'
    });

    // Change price
    items.push({
      id: 'change-price',
      label: 'Change price',
      icon: 'dollar-sign',
      iconColor: 'text-blue-500'
    });

    // Add special price
    items.push({
      id: 'add-special-price',
      label: 'Add special price',
      icon: 'tag',
      iconColor: 'text-blue-500'
    });

    // Delete custom prices
    items.push({
      id: 'delete-custom-prices',
      label: 'Delete custom prices',
      icon: 'times-circle',
      iconColor: 'text-red-500'
    });

    // Delete all
    items.push({
      id: 'delete-all',
      label: 'Delete all',
      icon: 'trash',
      iconColor: 'text-red-500'
    });

    return items;
  }

  onBulkActionMenuItemClick(itemId: string): void {
    switch (itemId) {
      case 'disable-all':
        this.disableAllSelected();
        break;
      case 'enable-all':
        this.enableAllSelected();
        break;
      case 'change-category':
        this.changeCategoryForSelected();
        break;
      case 'change-price':
        this.changePriceForSelected();
        break;
      case 'add-special-price':
        this.addSpecialPriceForSelected();
        break;
      case 'delete-custom-prices':
        this.deleteCustomPricesForSelected();
        break;
      case 'delete-all':
        this.deleteAllSelected();
        break;
    }
  }

  getCategoryMenuItems(_category: ExtendedCategoryRes): any[] {
    const items = [];

    // Edit
    items.push({
      id: 'edit-category',
      label: 'Edit',
      icon: 'edit',
      iconColor: 'text-blue-500'
    });

    // Disable all
    items.push({
      id: 'disable-category',
      label: 'Disable all',
      icon: 'ban',
      iconColor: 'text-gray-500'
    });

    // Enable all
    items.push({
      id: 'enable-category',
      label: 'Enable all',
      icon: 'check',
      iconColor: 'text-green-500'
    });

    // Sort by price (Low to High)
    items.push({
      id: 'sort-low-high',
      label: 'Sort by price (Low to High)',
      icon: 'sort-amount-down',
      iconColor: 'text-blue-500'
    });

    // Sort by price (High to Low)
    items.push({
      id: 'sort-high-low',
      label: 'Sort by price (High to Low)',
      icon: 'sort-amount-up',
      iconColor: 'text-blue-500'
    });

    // Remove
    items.push({
      id: 'remove-category',
      label: 'Remove',
      icon: 'trash',
      iconColor: 'text-red-500'
    });

    return items;
  }

  onCategoryMenuItemClick(itemId: string, category: ExtendedCategoryRes): void {
    switch (itemId) {
      case 'edit-category':
        this.editCategory(category);
        break;
      case 'disable-category':
        this.disableCategory(category);
        break;
      case 'enable-category':
        this.enableCategory(category);
        break;
      case 'sort-low-high':
        this.sortCategoryByPriceLowToHigh(category);
        break;
      case 'sort-high-low':
        this.sortCategoryByPriceHighToLow(category);
        break;
      case 'remove-category':
        this.removeCategory(category);
        break;
    }
  }

  getServiceMenuItems(service: SuperGeneralSvRes): any[] {
    const items = [];

    // Notify resellers
    items.push({
      id: 'notify-resellers',
      label: 'menu.service.notify_resellers',
      icon: 'bell',
      iconColor: 'text-blue-600',
      disabled: false
    });

    // Edit service
    items.push({
      id: 'edit-service',
      label: 'menu.service.edit_service',
      icon: 'edit',
      iconColor: 'text-blue-600',
      disabled: false
    });

    // Change category
    items.push({
      id: 'change-category',
      label: 'menu.service.change_category',
      icon: 'folder',
      iconColor: 'text-orange-600',
      disabled: false
    });

    // Duplicate
    items.push({
      id: 'duplicate',
      label: 'menu.service.duplicate_service',
      icon: 'copy',
      iconColor: 'text-green-600',
      disabled: false
    });

    // Special prices
    items.push({
      id: 'special-prices',
      label: 'menu.service.special_prices',
      icon: 'star',
      iconColor: 'text-yellow-600',
      disabled: false
    });

    // Status toggle - Disable/Enable based on current status
    if (this.isServiceEnabled(service)) {
      items.push({
        id: 'disable-service',
        label: 'menu.service.disable_service',
        icon: 'ban',
        iconColor: 'text-gray-500',
        disabled: false
      });
    } else {
      items.push({
        id: 'enable-service',
        label: 'menu.service.enable_service',
        icon: 'check',
        iconColor: 'text-green-500',
        disabled: false
      });
    }

    // Delete
    items.push({
      id: 'delete',
      label: 'menu.service.delete_service',
      icon: 'trash',
      iconColor: 'text-red-600',
      disabled: false
    });

    return items;
  }

  onServiceMenuItemClick(itemId: string, service: SuperGeneralSvRes): void {
    switch (itemId) {
      case 'notify-resellers':
        this.notifyResellers(service);
        break;
      case 'edit-service':
        this.editService(service);
        break;
      case 'change-category':
        this.changeServiceCategory(service);
        break;
      case 'duplicate':
        this.duplicateService(service);
        break;
      case 'special-prices':
        this.addSpecialPriceForService(service);
        break;
      case 'disable-service':
      case 'enable-service':
        this.toggleServiceStatus(service);
        break;
      case 'delete':
        this.deleteService(service);
        break;
    }
  }

  // Bulk action implementations
  enableAllSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }

    this.uiStateService.setBulkOperationLoading(true, `Enabling ${selectedServices.size} services...`);
    this.serviceManagementService.enableAllSelected(selectedServices, (completedCount, errorCount) => {
      this.uiStateService.setBulkOperationLoading(false);
      this.toastService.showSuccess(`Enabled ${completedCount} services`);
      if (errorCount > 0) {
        this.toastService.showError(`Failed to enable ${errorCount} services`);
      }
    });
  }

  disableAllSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }

    this.uiStateService.setBulkOperationLoading(true, `Disabling ${selectedServices.size} services...`);
    this.serviceManagementService.disableAllSelected(selectedServices, (completedCount, errorCount) => {
      this.uiStateService.setBulkOperationLoading(false);
      this.toastService.showSuccess(`Disabled ${completedCount} services`);
      if (errorCount > 0) {
        this.toastService.showError(`Failed to disable ${errorCount} services`);
      }
    });
  }

  changeCategoryForSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }
    this.uiStateService.openCategorySelectionModal();
  }

  changePriceForSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }
    this.uiStateService.openNewPricesModal();
  }

  addSpecialPriceForSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }
    this.uiStateService.openNewSpecialPricesModal();
  }

  deleteCustomPricesForSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }

    // Show custom confirmation dialog
    this.bulkDeleteType = 'custom-prices';
    this.bulkDeleteCount = selectedServices.size;
    this.bulkDeleteMessage = `${selectedServices.size} selected services`;
    this.showBulkDeleteConfirmation = true;
  }

  deleteAllSelected(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) {
      this.toastService.showWarning('No services selected');
      return;
    }

    // Show custom confirmation dialog
    this.bulkDeleteType = 'services';
    this.bulkDeleteCount = selectedServices.size;
    this.bulkDeleteMessage = `${selectedServices.size} selected services`;
    this.showBulkDeleteConfirmation = true;
  }

  // Category action implementations
  editCategory(category: ExtendedCategoryRes): void {
    this.categoryToEdit = category;
    this.uiStateService.openNewCategoryModal();
  }

  disableCategory(category: ExtendedCategoryRes): void {
    const servicesToDisable = category.services.filter(service => service.status === 'ACTIVATED');
    if (servicesToDisable.length === 0) {
      this.toastService.showWarning('No active services to disable in this category');
      return;
    }

    this.uiStateService.setBulkOperationLoading(true, `Disabling ${servicesToDisable.length} services...`);

    let completedCount = 0;
    let errorCount = 0;

    servicesToDisable.forEach(service => {
      this.adminServiceService.deactivateService(service.id).subscribe({
        next: () => {
          completedCount++;
          if (completedCount + errorCount === servicesToDisable.length) {
            this.uiStateService.setBulkOperationLoading(false);
            this.toastService.showSuccess(`Disabled ${completedCount} services in ${category.name}`);
            if (errorCount > 0) {
              this.toastService.showError(`Failed to disable ${errorCount} services`);
            }
            this.loadPlatforms();
          }
        },
        error: () => {
          errorCount++;
          if (completedCount + errorCount === servicesToDisable.length) {
            this.uiStateService.setBulkOperationLoading(false);
            this.toastService.showSuccess(`Disabled ${completedCount} services in ${category.name}`);
            if (errorCount > 0) {
              this.toastService.showError(`Failed to disable ${errorCount} services`);
            }
            this.loadPlatforms();
          }
        }
      });
    });
  }

  enableCategory(category: ExtendedCategoryRes): void {
    const servicesToEnable = category.services.filter(service => service.status !== 'ACTIVATED');
    if (servicesToEnable.length === 0) {
      this.toastService.showWarning('No inactive services to enable in this category');
      return;
    }

    this.uiStateService.setBulkOperationLoading(true, `Enabling ${servicesToEnable.length} services...`);

    let completedCount = 0;
    let errorCount = 0;

    servicesToEnable.forEach(service => {
      this.adminServiceService.activateService(service.id).subscribe({
        next: () => {
          completedCount++;
          if (completedCount + errorCount === servicesToEnable.length) {
            this.uiStateService.setBulkOperationLoading(false);
            this.toastService.showSuccess(`Enabled ${completedCount} services in ${category.name}`);
            if (errorCount > 0) {
              this.toastService.showError(`Failed to enable ${errorCount} services`);
            }
            this.loadPlatforms();
          }
        },
        error: () => {
          errorCount++;
          if (completedCount + errorCount === servicesToEnable.length) {
            this.uiStateService.setBulkOperationLoading(false);
            this.toastService.showSuccess(`Enabled ${completedCount} services in ${category.name}`);
            if (errorCount > 0) {
              this.toastService.showError(`Failed to enable ${errorCount} services`);
            }
            this.loadPlatforms();
          }
        }
      });
    });
  }

  sortCategoryByPriceLowToHigh(category: ExtendedCategoryRes): void {
    category.services.sort((a, b) => (a.price || 0) - (b.price || 0));
    this.toastService.showSuccess(`Sorted ${category.name} by price (Low to High)`);
  }

  sortCategoryByPriceHighToLow(category: ExtendedCategoryRes): void {
    category.services.sort((a, b) => (b.price || 0) - (a.price || 0));
    this.toastService.showSuccess(`Sorted ${category.name} by price (High to Low)`);
  }

  removeCategory(category: ExtendedCategoryRes): void {
    // Check if category has active services
    const hasActiveServices = category.services.some(service => service.status === 'ACTIVATED'); // ACTIVATED
    const hasDisabledServices = category.services.some(service => service.status === 'DEACTIVATED'); // DEACTIVATED

    if (hasActiveServices) {
      this.toastService.showError('Cannot delete category with active services. Please disable or move all active services first.');
      return;
    }

    // Show custom confirmation dialog
    this.categoryToDelete = category;
    this.showCategoryDeleteConfirmation = true;
  }

  // Service action implementations
  notifyResellers(_service: SuperGeneralSvRes): void {
    this.toastService.showSuccess('Notifying resellers...');
    // Implementation for notifying resellers
  }

  editService(service: SuperGeneralSvRes): void {
    const serviceCopy = JSON.parse(JSON.stringify(service));
    this.uiStateService.selectedServiceForAction = serviceCopy;
    const serviceType = serviceCopy.add_type === AddType.Api ? 'Provider' : 'Manual';
    this.uiStateService.openNewServiceModalForEdit(serviceCopy, serviceType);
  }

  changeServiceCategory(service: SuperGeneralSvRes): void {
    this.uiStateService.selectedServiceForAction = service;
    this.uiStateService.openCategorySelectionModal();
  }

  duplicateService(service: SuperGeneralSvRes): void {
    console.log('Duplicating service:', service);

    // Close the service action menu
    this.uiStateService.closeServiceActionMenu();

    // Show loading
    this.uiStateService.setLoading(true, 'Duplicating service...');

    // Call the API to duplicate the service
    this.adminServiceService.duplicateService(service.id).subscribe({
      next: (duplicatedService) => {
        this.uiStateService.setLoading(false);
        this.toastService.showSuccess(`Service "${service.name}" duplicated successfully`);
        console.log('Service duplicated:', duplicatedService);

        // Reload platforms to show the new duplicated service
        this.loadPlatforms();
      },
      error: (error) => {
        this.uiStateService.setLoading(false);
        console.error('Error duplicating service:', error);
        this.toastService.showError('Failed to duplicate service. Please try again.');
      }
    });
  }

  addSpecialPriceForService(service: SuperGeneralSvRes): void {
    console.log('Add special price for service:', service);

    // Set the selected service for action BEFORE closing menu
    this.uiStateService.selectedServiceForAction = service;

    // Close the service action menu
    this.uiStateService.showServiceActionMenu = false;

    // Show the popup immediately
    this.uiStateService.openSpecialPricesServiceModal();

    console.log('Modal should be open now. showSpecialPricesServiceModal:', this.uiStateService.showSpecialPricesServiceModal);
    console.log('Selected service:', this.uiStateService.selectedServiceForAction);
  }

  deleteService(service: SuperGeneralSvRes): void {
    this.serviceToDelete = service;
    this.showDeleteConfirmation = true;
  }

  closeDeleteConfirmation(): void {
    this.showDeleteConfirmation = false;
    this.serviceToDelete = null;
  }

  confirmDeleteService(): void {
    if (!this.serviceToDelete) return;

    this.isDeleting = true;
    this.adminServiceService.deleteService(this.serviceToDelete.id).subscribe({
      next: () => {
        this.toastService.showSuccess('Service deleted successfully');
        this.closeDeleteConfirmation();
        this.loadPlatforms();
               this.isDeleting = false;
      },
      error: (error) => {
        console.error('Error deleting service:', error);
        this.toastService.showError('Failed to delete service');
        this.isDeleting = false;
      }
    });
  }

  // Bulk delete confirmation methods
  closeBulkDeleteConfirmation(): void {
    this.showBulkDeleteConfirmation = false;
    this.bulkDeleteType = 'services';
    this.bulkDeleteMessage = '';
    this.bulkDeleteCount = 0;
  }

  confirmBulkDelete(): void {
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size === 0) return;

    if (this.bulkDeleteType === 'custom-prices') {
      this.uiStateService.setBulkOperationLoading(true, `Deleting custom prices for ${selectedServices.size} services...`);

      // Implementation for deleting custom prices
      setTimeout(() => {
        this.uiStateService.setBulkOperationLoading(false);
        this.toastService.showSuccess(`Custom prices deleted for ${selectedServices.size} services`);
        this.selectionService.clearSelectedServices();
        this.loadPlatforms();
        this.closeBulkDeleteConfirmation();
      }, 1000);
    } else if (this.bulkDeleteType === 'services') {
      this.uiStateService.setBulkOperationLoading(true, `Deleting ${selectedServices.size} services...`);

      let completedCount = 0;
      let errorCount = 0;

      selectedServices.forEach(service => {
        this.adminServiceService.deleteService(service).subscribe({
          next: () => {
            completedCount++;
            if (completedCount + errorCount === selectedServices.size) {
              this.uiStateService.setBulkOperationLoading(false);
              this.toastService.showSuccess(`Deleted ${completedCount} services`);
              if (errorCount > 0) {
                this.toastService.showError(`Failed to delete ${errorCount} services`);
              }
              this.selectionService.clearSelectedServices();
              this.loadPlatforms();
              this.closeBulkDeleteConfirmation();
            }
          },
          error: () => {
            errorCount++;
            if (completedCount + errorCount === selectedServices.size) {
              this.uiStateService.setBulkOperationLoading(false);
              this.toastService.showSuccess(`Deleted ${completedCount} services`);
              if (errorCount > 0) {
                this.toastService.showError(`Failed to delete ${errorCount} services`);
              }
              this.selectionService.clearSelectedServices();
              this.loadPlatforms();
              this.closeBulkDeleteConfirmation();
            }
          }
        });
      });
    }
  }

  // Category delete confirmation methods
  closeCategoryDeleteConfirmation(): void {
    this.showCategoryDeleteConfirmation = false;
    this.categoryToDelete = null;
    this.isCategoryDeleting = false;
  }

  confirmCategoryDelete(): void {
    if (!this.categoryToDelete) return;

    const category = this.categoryToDelete;
    const hasDisabledServices = category.services.some(service => service.status === 'DEACTIVATED');

    this.isCategoryDeleting = true;
    this.uiStateService.setBulkOperationLoading(true, `Deleting category "${category.name}"...`);

    this.adminServiceService.deleteCategory(category.id).subscribe({
      next: () => {
        if (hasDisabledServices) {
          this.toastService.showSuccess(`Category "${category.name}" and all its disabled services deleted successfully`);
        } else {
          this.toastService.showSuccess(`Category "${category.name}" deleted successfully`);
        }
        this.uiStateService.setBulkOperationLoading(false);
        this.loadPlatforms();
        this.closeCategoryDeleteConfirmation();
      },
      error: (error) => {
        console.error('Error deleting category:', error);
        this.uiStateService.setBulkOperationLoading(false);
        this.isCategoryDeleting = false;

        // Handle specific error messages from backend
        if (error.error?.message) {
          this.toastService.showError(error.error.message);
        } else {
          this.toastService.showError('Failed to delete category. Please try again.');
        }
      }
    });
  }

  // CDK Drag and drop methods for categories
  onCategoryDragStarted(event: CdkDragStart): void {
    // Set dragging state
    this.isDragging = true;

    // Get the width of the original element
    const element = event.source.element.nativeElement;
    const width = element.offsetWidth;

    // Set the CSS variable for the drag preview width
    document.documentElement.style.setProperty('--category-width', `${width}px`);

    // Add visual feedback to the dragged element
    element.classList.add('cdk-drag-dragging');

    // Add a slight delay to ensure smooth animation start
    setTimeout(() => {
      element.style.opacity = '0.6';
    }, 50);
  }

  onCategoryDropCdk(event: CdkDragDrop<ExtendedCategoryRes[]>): void {
    // Reset dragging state
    this.isDragging = false;

    if (event.previousIndex === event.currentIndex) {
      return; // No change in position
    }

    // Get the categories being reordered
    const category1 = this.displayCategories[event.previousIndex];
    const category2 = this.displayCategories[event.currentIndex];

    // Update the local array using CDK's moveItemInArray
    moveItemInArray(this.displayCategories, event.previousIndex, event.currentIndex);

    // Call the API to update the order in the backend
    this.updateCategoryOrder(category1, category2);
  }

  // Category move mode drop handler
  onCategoryMoveModeDropCdk(event: CdkDragDrop<ExtendedCategoryRes[]>): void {
    // Reset dragging state
    this.isDragging = false;

    if (event.previousIndex === event.currentIndex) {
      return; // No change in position
    }

    // Get the categories being reordered
    const category1 = this.displayCategories[event.previousIndex];
    const category2 = this.displayCategories[event.currentIndex];

    // Update the local array using CDK's moveItemInArray
    moveItemInArray(this.displayCategories, event.previousIndex, event.currentIndex);

    // Call the API to update the order in the backend
    this.updateCategoryOrder(category1, category2);

    // Note: The main table will automatically reflect the new order when user exits move mode
    // because both tables use the same displayCategories array
  }

  updateCategoryOrder(category1?: ExtendedCategoryRes, category2?: ExtendedCategoryRes): void {
    // Implementation to update category order in backend using swap API
    if (category1 && category2) {
      console.log(`Reordering ${category1.name} and ${category2.name}...`);

      this.adminServiceService.swapCategorySort(category1.id, category2.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Category order updated successfully');
        },
        error: (error) => {
          console.error('Error updating category order:', error);
          this.toastService.showError('Failed to update category order');
          // Revert the local change
          this.loadPlatforms();
        }
      });
    } else {
      this.toastService.showSuccess('Updating category order...');
    }
  }

  // Drag and drop methods for services
  onServiceDropCdk(event: CdkDragDrop<SuperGeneralSvRes[]>, category: ExtendedCategoryRes): void {
    // Reset dragging state
    this.isDragging = false;

    if (event.previousContainer === event.container) {
      // Reorder within same category
      if (event.previousIndex === event.currentIndex) {
        return; // No change
      }

      const service1 = category.services[event.previousIndex];
      const service2 = category.services[event.currentIndex];

      // Update local array first
      moveItemInArray(category.services, event.previousIndex, event.currentIndex);

      // Call API to update order
      this.updateServiceOrder(category, service1, service2);
    } else {
      // Move between categories
      const previousCategory = this.findCategoryByServices(event.previousContainer.data);
      if (previousCategory) {
        const movedService = previousCategory.services[event.previousIndex];

        // Update local arrays first
        previousCategory.services.splice(event.previousIndex, 1);
        category.services.splice(event.currentIndex, 0, movedService);

        // Call API to move service to new category with position
        this.moveServiceToCategory(movedService, category, previousCategory, event.currentIndex);
      }
    }
  }

  onServiceDragStarted(event: CdkDragStart): void {
    // Set dragging state
    this.isDragging = true;

    // Get the width of the original element
    const element = event.source.element.nativeElement;
    const width = element.offsetWidth;

    // Set the CSS variable for the drag preview width
    document.documentElement.style.setProperty('--service-width', `${width}px`);

    // Add visual feedback to the dragged element
    element.classList.add('cdk-drag-dragging');

    // Add a slight delay to ensure smooth animation start
    setTimeout(() => {
      element.style.opacity = '0.6';
    }, 50);
  }

  // Handle drag moved (to ensure isDragging stays true)
  onServiceDragMoved(event: any): void {
    if (!this.isDragging) {
      this.isDragging = true;
    }
  }

  // Handle drag ended (for cases where drag is cancelled without drop)
  onDragEnded(): void {
    this.isDragging = false;
    this.dragOverCategoryIndex = -1;
  }

  // Allow drop predicate to ensure empty categories can receive drops
  allowDrop = (): boolean => {
    return true; // Allow all drops - CDK will handle the actual drop logic
  }

  onDragEntered(category: ExtendedCategoryRes): void {
    // Find the index of the category being dragged over
    const categoryIndex = this.displayCategories.findIndex(cat => cat.id === category.id);
    this.dragOverCategoryIndex = categoryIndex;
  }

  onDragExited(): void {
    this.dragOverCategoryIndex = -1;
  }

  getConnectedDropLists(currentCategory: ExtendedCategoryRes): string[] {
    // Return array of all category service drop list IDs except current one
    return this.displayCategories
      .filter(cat => cat.id !== currentCategory.id)
      .map(cat => `category-services-${cat.id}`);
  }

  findCategoryByServices(services: SuperGeneralSvRes[]): ExtendedCategoryRes | null {
    return this.displayCategories.find(cat => cat.services === services) || null;
  }

  updateServiceOrder(category: ExtendedCategoryRes, service1?: SuperGeneralSvRes, service2?: SuperGeneralSvRes): void {
    // Implementation to update service order in backend using reorder API
    if (service1 && service2) {
      console.log(`Reordering ${service1.name} and ${service2.name} in ${category.name}...`);

      this.adminServiceService.reorderServicePositions(service1.id, service2.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Service order updated successfully');
        },
        error: (error) => {
          console.error('Error updating service order:', error);
          this.toastService.showError('Failed to update service order');
          // Revert the local change
          this.loadPlatforms();
        }
      });
    } else {
      this.toastService.showSuccess('Updating service order...');
    }
  }

  moveServiceToCategory(service: SuperGeneralSvRes, targetCategory: ExtendedCategoryRes, sourceCategory?: ExtendedCategoryRes, targetIndex?: number): void {
    // Implementation to move service to different category using changeCategoryWithPosition API
    const sourceName = sourceCategory ? sourceCategory.name : 'previous category';
    console.log(`Moving ${service.name} from ${sourceName} to ${targetCategory.name}...`);

    // If targetIndex is not provided, append to the end
    const position = targetIndex !== undefined ? targetIndex : targetCategory.services.length;

    this.adminServiceService.changeServiceCategoryWithPosition(service.id, targetCategory.id, position).subscribe({
      next: () => {
        this.toastService.showSuccess(`Service moved to ${targetCategory.name} successfully`);
        // Reload platforms to reflect the category change
       // this.loadPlatforms();
      },
      error: (error) => {
        console.error('Error moving service to category:', error);
        this.toastService.showError('Failed to move service');
        // Revert the local change
        if (sourceCategory) {
          // Move service back to original category
          const serviceIndex = targetCategory.services.findIndex(s => s.id === service.id);
          if (serviceIndex !== -1) {
            targetCategory.services.splice(serviceIndex, 1);
            sourceCategory.services.push(service);
          }
        }
        this.loadPlatforms();
      }
    });
  }

  // Category platform dropdown methods
  toggleCategoryPlatformDropdown(event: MouseEvent, category: ExtendedCategoryRes): void {
    event.stopPropagation();

    if (this.showCategoryPlatformDropdown && this.selectedCategoryForPlatform?.id === category.id) {
      this.uiStateService.closeCategoryPlatformDropdown();
      return;
    }

    // Use the UI state service method to handle platform selection initialization
    this.uiStateService.toggleCategoryPlatformDropdown(event, category, this.categoryPlatformSelections, this.allPlatforms);

    // Set menu position (now positioning above the button)
    const rect = (event.target as HTMLElement).getBoundingClientRect();
    this.uiStateService.categoryPlatformMenuPosition = {
      top: rect.top + window.scrollY - 10, // Position above the button
      left: rect.left + window.scrollX
    };
  }

  selectCategoryPlatform(category: ExtendedCategoryRes, platform: SuperPlatformRes): void {
    this.platformService.selectCategoryPlatform(category, platform, this.categoryPlatformSelections);
    this.uiStateService.closeCategoryPlatformDropdown();

    // Refresh platforms list after changing a category's platform
    setTimeout(() => {
      this.loadPlatforms();
    }, 500); // Add a small delay to ensure the API call completes

    this.toastService.showSuccess(`Change platform success`);
  }

  isCategoryPlatformSelected(category: ExtendedCategoryRes, platformId: number): boolean {
    return this.platformService.isCategoryPlatformSelected(category.id, platformId, this.categoryPlatformSelections);
  }

  addNewPlatformFromCategory(event: MouseEvent): void {
    event.stopPropagation();
    this.uiStateService.closeCategoryPlatformDropdown();
    this.uiStateService.openPlatformManagementModal();
  }

  // Category visibility toggle
  onToggleCategoryVisibility(category: ExtendedCategoryRes, currentHideState: boolean): void {
    category.hide = !currentHideState;

    // Check if we should auto-enable/disable move mode
    setTimeout(() => {
      this.checkAutoMoveMode();
    }, 100);
  }

  // Category move mode methods
  toggleCategoryMoveMode(): void {
    if (this.isCategoryMoveMode) {
      // Exit move mode - open all categories
      this.isCategoryMoveMode = false;
      this.openAllCategories();
    } else {
      // Enter move mode - close all categories
      this.isCategoryMoveMode = true;
      this.closeAllCategories();
    }
  }

  closeAllCategories(): void {
    this.displayCategories.forEach(category => {
      if (category.services.length > 0) {
        category.hide = true;
      }
    });
  }

  openAllCategories(): void {
    this.displayCategories.forEach(category => {
      if (category.services.length > 0) {
        category.hide = false;
      }
    });
  }

  areAllCategoriesClosed(): boolean {
    return this.displayCategories
      .filter(cat => cat.services.length > 0)
      .every(cat => cat.hide);
  }

  // Check if we should auto-enable/disable move mode based on category states
  checkAutoMoveMode(): void {
    // Don't auto-enable move mode when filter is applied
    const hasFilter = this.isFilterApplied ||
                     this.searchTerm.trim() !== '' ||
                     (this.selectedCategory && this.selectedCategory.id !== 'all') ||
                     (this.selectedApiProvider && this.selectedApiProvider.id !== 'all');

    const allClosed = this.areAllCategoriesClosed();

    if (!this.isCategoryMoveMode && allClosed && !hasFilter) {
      // Auto-enable move mode when all categories are closed and no filter is applied
      this.isCategoryMoveMode = true;
    } else if (this.isCategoryMoveMode && (!allClosed || hasFilter)) {
      // Auto-disable move mode when at least one category is opened or filter is applied
      this.isCategoryMoveMode = false;
    }
  }

  // Exit move mode and expand specific category
  exitMoveAndExpandCategory(category: ExtendedCategoryRes): void {
    // Exit move mode
    this.isCategoryMoveMode = false;

    // Expand the specific category
    category.hide = false;

    // Keep other categories closed initially for clean UX
    // User can expand others manually if needed
  }

  // Utility methods
  extractDomainName(url: string): string {
    if (!url) return '';
    try {
      const domain = url.replace(/^https?:\/\//, '').split('/')[0];
      return domain;
    } catch {
      return url;
    }
  }

  copyToClipboard(text: string, event: MouseEvent): void {
    event.stopPropagation();
    navigator.clipboard.writeText(text).then(() => {
      this.toastService.showSuccess('Copied to clipboard');
    }).catch(() => {
      this.toastService.showError('Failed to copy');
    });
  }

  // Event handlers for modal callbacks
  onServiceAdded(_service: SuperGeneralSvRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Service added successfully');
  }

  onPlatformAdded(_platform: SuperPlatformRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Platform added successfully');
  }

  onPlatformsUpdated(_platforms: SuperPlatformRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Platforms updated successfully');
  }

  onCategoryAdded(_category: SuperCategoryRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Category added successfully');
  }

  onCategoryUpdated(_category: SuperCategoryRes): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Category updated successfully');
  }

  onCategorySelectedForMove(category: SuperCategoryRes): void {
    // Check if we have a single service selected for category change
    if (this.selectedServiceForAction) {
      this.moveServiceToCategory(this.selectedServiceForAction, category as any);
      this.uiStateService.closeCategorySelectionModal();
      return;
    }

    // Handle bulk category change for multiple selected services
    const selectedServices = this.selectionService.getSelectedServices();
    if (selectedServices.size > 0) {
      this.changeBulkServiceCategory(selectedServices, category);
    }
  }

  private changeBulkServiceCategory(selectedServiceIds: Set<number>, targetCategory: SuperCategoryRes): void {
    const selectedServicesArray = Array.from(selectedServiceIds);
    let completedCount = 0;
    let errorCount = 0;
    const totalServices = selectedServicesArray.length;

    // Show loading indicator
    this.toastService.showSuccess(`Changing category for ${totalServices} services...`);

    selectedServicesArray.forEach(serviceId => {
      this.adminServiceService.changeServiceCategory(serviceId, targetCategory.id).subscribe({
        next: () => {
          completedCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === totalServices) {
            this.handleBulkCategoryChangeComplete(completedCount, errorCount, targetCategory.name);
          }
        },
        error: (error) => {
          console.error('Error changing service category:', error);
          errorCount++;

          // Check if all operations are complete
          if (completedCount + errorCount === totalServices) {
            this.handleBulkCategoryChangeComplete(completedCount, errorCount, targetCategory.name);
          }
        }
      });
    });
  }

  private handleBulkCategoryChangeComplete(completedCount: number, errorCount: number, categoryName: string): void {
    // Clear selection and close modal
    this.selectionService.clearSelectedServices();
    this.uiStateService.closeCategorySelectionModal();

    // Reload data to reflect changes
    this.loadPlatforms();

    // Show completion message
    if (errorCount === 0) {
      this.toastService.showSuccess(`Successfully moved ${completedCount} services to ${categoryName}`);
    } else if (completedCount > 0) {
      this.toastService.showWarning(`Moved ${completedCount} services to ${categoryName}, ${errorCount} failed`);
    } else {
      this.toastService.showError(`Failed to move services to ${categoryName}`);
    }
  }

  onPricesUpdated(_services: SuperGeneralSvRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Prices updated successfully');
  }

  onSpecialPriceAdded(_specialPrice: SpecialPriceRes | SpecialPriceRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess('Special price added successfully');
  }

  onServicesImported(services: SuperGeneralSvRes[]): void {
    this.loadPlatforms();
    this.toastService.showSuccess(`${services.length} services imported successfully`);
  }

  // Resources modal methods
  closeResourcesModal(): void {
    this.showResourcesModal = false;
  }

  openNewServiceFromResources(serviceType: string): void {
    this.showResourcesModal = false;
    this.uiStateService.openNewServiceModal(serviceType);
  }

  closeNewServiceModal(): void {
    this.uiStateService.closeNewServiceModal();
  }

  openResourcesFromNewService(): void {
    this.showResourcesModal = true;
  }
}
